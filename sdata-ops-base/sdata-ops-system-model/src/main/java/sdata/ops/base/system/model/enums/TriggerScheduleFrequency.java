package sdata.ops.base.system.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 触发器调度频率枚举
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Getter
public enum TriggerScheduleFrequency {

    /**
     * 默认值/未设置 - 对应数据库中的字符串 "0"
     */
    NONE("0", "none", "未设置", 0),

    /**
     * 30分钟
     */
    THIRTY_MINUTES("30m", "30m", "30分钟", 30),

    /**
     * 1小时
     */
    ONE_HOUR("1h", "1h", "1小时", 60),

    /**
     * 2小时
     */
    TWO_HOURS("2h", "2h", "2小时", 120);

    /**
     * 数据库存储值 - 对应 VARCHAR 字段
     */
    @EnumValue
    private final String value;

    /**
     * 频率代码
     */
    private final String code;

    /**
     * 频率描述
     */
    private final String description;

    /**
     * 间隔分钟数
     */
    private final int intervalMinutes;

    TriggerScheduleFrequency(String value, String code, String description, int intervalMinutes) {
        this.value = value;
        this.code = code;
        this.description = description;
        this.intervalMinutes = intervalMinutes;
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public int getIntervalMinutes() {
        return intervalMinutes;
    }

    /**
     * 根据数据库值获取枚举
     *
     * @param value 数据库存储值
     * @return 枚举值
     */
    public static TriggerScheduleFrequency fromValue(String value) {
        if (value == null) {
            return NONE;
        }
        for (TriggerScheduleFrequency frequency : values()) {
            if (frequency.getValue().equals(value)) {
                return frequency;
            }
        }
        return NONE; // 默认返回未设置状态
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 频率代码
     * @return 枚举值
     */
    public static TriggerScheduleFrequency fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (TriggerScheduleFrequency frequency : values()) {
            if (frequency.getCode().equals(code)) {
                return frequency;
            }
        }
        throw new IllegalArgumentException("未知的触发器调度频率代码: " + code);
    }

    /**
     * 验证代码是否有效
     *
     * @param code 频率代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (code == null) {
            return false;
        }
        for (TriggerScheduleFrequency frequency : values()) {
            if (frequency.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
