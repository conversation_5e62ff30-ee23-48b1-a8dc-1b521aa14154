-- 修复 TriggerScheduleFrequency 枚举映射问题
-- 解决 "No enum constant sdata.ops.base.system.model.enums.TriggerScheduleFrequency.0" 错误
-- 问题原因：数据库字段是 VARCHAR(10)，存储字符串 "0"，但枚举没有对应的值
-- 解决方案：在枚举中添加 NONE("0", ...) 来处理字符串 "0"
-- 创建时间: 2025-09-25

-- 查看当前数据分布
SELECT
    trigger_schedule_frequency,
    COUNT(*) as count
FROM ops_task_attr_basic
WHERE trigger_schedule_frequency IS NOT NULL
GROUP BY trigger_schedule_frequency;

-- 枚举映射关系：
-- "0"   -> NONE (未设置)
-- "30m" -> THIRTY_MINUTES (30分钟)
-- "1h"  -> ONE_HOUR (1小时)
-- "2h"  -> TWO_HOURS (2小时)

-- 可选：如果需要将现有的字符串 "0" 数据设置为有效值
-- 注意：执行前请确认业务需求

-- 将字符串 "0" 的记录更新为 NULL（表示未设置）
-- UPDATE ops_task_attr_basic
-- SET trigger_schedule_frequency = NULL
-- WHERE trigger_schedule_frequency = '0';

-- 或者将字符串 "0" 的记录更新为默认的 30 分钟频率
-- UPDATE ops_task_attr_basic
-- SET trigger_schedule_frequency = '30m'
-- WHERE trigger_schedule_frequency = '0';

-- 验证更新后的数据
-- SELECT 
--     trigger_schedule_frequency,
--     COUNT(*) as count
-- FROM ops_task_attr_basic 
-- WHERE trigger_schedule_frequency IS NOT NULL
-- GROUP BY trigger_schedule_frequency;
