-- 为 OpsTaskAttrBasicReplica 表添加工作流绑定相关字段
-- 用于支持任务单元副本与工作流触发器的绑定功能
-- 创建时间: 2025-09-25

-- 新增工作流ID字段
ALTER TABLE ops_task_attr_basic_replica 
ADD COLUMN workflow_id VARCHAR(64) COMMENT '绑定的工作流ID，用于工作流触发器功能';

-- 新增触发器调度频率字段
ALTER TABLE ops_task_attr_basic_replica
ADD COLUMN trigger_schedule_frequency INT COMMENT '触发器调度频率：0=30分钟、1=1小时、2=2小时';

-- 为工作流ID字段添加索引，提高查询性能
CREATE INDEX idx_ops_task_attr_basic_replica_workflow_id ON ops_task_attr_basic_replica(workflow_id);

-- 查看添加结果
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ops_task_attr_basic_replica' 
  AND COLUMN_NAME IN ('workflow_id', 'trigger_schedule_frequency')
ORDER BY ORDINAL_POSITION;
