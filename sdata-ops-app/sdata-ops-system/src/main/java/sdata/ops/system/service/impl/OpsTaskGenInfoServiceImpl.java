package sdata.ops.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.dto.ConditionTaskDTO;
import sdata.ops.base.system.model.dto.OrgListDTO;
import sdata.ops.base.system.model.dto.RequireOptionDTO;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.base.system.model.vo.*;
import sdata.ops.common.api.TaskConstant;
import sdata.ops.common.core.util.CronGeneric;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.job.TaskException;
import sdata.ops.system.mapper.OpsTaskGenInfoMapper;
import sdata.ops.system.service.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 针对表【ops_task_gen_info】的数据库操作Service实现
 * @createDate 2024-07-04 10:28:39
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpsTaskGenInfoServiceImpl extends ServiceImpl<OpsTaskGenInfoMapper, OpsTaskGenInfo>
        implements OpsTaskGenInfoService {

    // 延迟转派服务类
    private final OpsTaskTransferConfService opsTaskTransferConfService;
    //配置服务来源服务类
    private final OpsTaskAttrBasicService opsTaskAttrBasicService;
    //任务配置单元与job关联id服务类
    private final OpsTaskJobRelationService relationService;
    //定时任务处理器
    //任务模板服务
    private final OpsTaskTemplateService taskTemplateService;
    //任务模板中任务单位副本服务
    private final OpsTaskAttrBasicReplicaService replicaService;
    //任务文件上传列表
    private final OpsTaskGenInfoFileService opsTaskGenInfoFileService;
    //任务交易日
    private final OpsTradeTypeService opsTradeTypeService;
    //任务特殊规则日志项
    private final OpsTradeTypeService tradeTypeService;
    //组织管理
    private final OpsSysOrgService opsSysOrgService;
    //交易日
    private final OpsSysCalendarService sysCalendarService;
    //自定义任务触发器创建完成
    private final OpsTaskDynamicLogService dynamicLogService;

    private final OpsTaskGenInfoAsyncService asyncService;

    private final OpsSysCalendarService calendarService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSingleTask(OpsTaskGenInfo info) {
        //查看是否是自动生成的任务，如果是定时生成需要配置生成任务，如果为手动则立即生成
        //立即插入
        info.setId(null);
        save(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSingleTask(OpsTaskGenInfo info, String taskId) {
        //查看是否是自动生成的任务，如果是定时生成需要配置生成任务，如果为手动则立即生成
        if (Objects.equals(TaskConstant.MANUAL, info.getTaskTriggerType())) {
            //立即插入
            info.setId(null);
            save(info);
            updateTaskBasicStatus(taskId);
        }
        if (!Objects.equals(TaskConstant.MANUAL, info.getTaskTriggerType())) {
            //创建一个定时任务，到时间插入
            saveSchedulerPlanByUnit(info, taskId);
            updateTaskBasicStatus(taskId);
        }
        //查看是否是自动完成的任务
        if (Objects.equals(TaskConstant.AUTO, info.getTaskCompleteType())) {
            //自动完成需要存储脚本执行id，做任务关联，并创建一个调度任务
            //todo
        }
        //查看是否需要自动稽核
        //如果需要自动稽核
        if (Objects.equals(info.getTaskAuditType(), "1")) {
            //自动稽核绑定一个脚本执行id和任务关联，并创建一个调度任务
            //todo
        }
        //查看是否需要告警,告警为统一定时任务轮询查询执行
    }

    /**
     * 更新任务单元状态，生效/上线
     *
     * @param taskId 任务单元id
     */
    private void updateTaskBasicStatus(String taskId) {
        OpsTaskAttrBasic up = new OpsTaskAttrBasic();
        up.setTaskStatus(1);
        up.setId(Long.valueOf(taskId));
        opsTaskAttrBasicService.updateById(up);
    }

    /**
     * 日常任务调度入口，包含单条任务日常生成与模板任务日常生成，模板任务占比90以上
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void schedulerProcessByDaily() {
        if (!opsTradeTypeService.checkWorkdayByToday()) {
            log.info("日常任务-执行失败,当天不是工作日");
            return;
        }
        List<OpsTaskJobRelation> relations = relationService.list();
        dailySingleTask(relations);
        dailyTemplateTask(relations);
    }

    /**
     * 日常单条任务定时调度配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void dailySingleTask(List<OpsTaskJobRelation> relations) {
        List<String> taskIds = relations.stream().filter(i -> i.getTaskType().equals("1")).map(OpsTaskJobRelation::getTaskId).collect(Collectors.toList());
        if (taskIds.isEmpty()) {
            log.info("daily-single-scheduler未发现日常任务需要执行");
            return;
        }
        LambdaQueryWrapper<OpsTaskAttrBasic> basics = new LambdaQueryWrapper<>();
        basics.in(OpsTaskAttrBasic::getId, taskIds);
        List<OpsTaskAttrBasic> arr = opsTaskAttrBasicService.list(basics);
        List<OpsTaskGenInfo> save = arr.stream().map(this::convertGenInfo).collect(Collectors.toList());
        saveBatch(save);
        log.info("daily-single-scheduler生成任务清单数量{}", save.size());
    }

    /**
     * 模板任务生成
     *
     * @param relations 关系表
     */
    @Transactional(rollbackFor = Exception.class)
    public void dailyTemplateTask(List<OpsTaskJobRelation> relations) {
        List<String> taskIds = relations.stream().filter(i -> i.getTaskType().equals("2")).map(OpsTaskJobRelation::getTaskId).collect(Collectors.toList());
        if (taskIds.isEmpty()) {
            log.info("daily-template-scheduler未发现日常任务需要执行");
            return;
        }
        LambdaQueryWrapper<OpsTaskAttrBasicReplica> basics = new LambdaQueryWrapper<>();
        basics.in(OpsTaskAttrBasicReplica::getId, taskIds);
        List<OpsTaskAttrBasicReplica> arr = replicaService.list(basics);
        List<OpsTaskGenInfo> save = arr.stream().map(this::convertGenInfo).collect(Collectors.toList());
        replaceIdAndFillChildIdsAndSort(save);
        saveBatch(save);
        log.info("daily-template-scheduler生成任务清单数量{}", save.size());
    }

    /**
     * 对模板引用的replica 内容首先进行id替换，插入清单表使用
     * 然后，排序根节点在前
     * 最后根节点要存储所有子级的id集合
     *
     * @param
     */
    @Override
    public void replaceIdAndFillChildIdsAndSort(List<OpsTaskGenInfo> save) {
        //生成新的清单，但是要保留 id-pid 的关系
        Map<Long, Long> idMapper = new HashMap<>();
        for (OpsTaskGenInfo info : save) {
            Long newId = IdWorker.getId();
            idMapper.put(info.getId(), newId);
            info.setId(newId);
        }
        for (OpsTaskGenInfo info : save) {
            if (info.getParentId() != 0L) {
                info.setParentId(idMapper.get(info.getParentId()) == null ? 0L : idMapper.get(info.getParentId()));
            }
            //如果有依赖,id也需要替换
            if (StringUtils.hasText(info.getDependOnIds())) {
                info.setDependOnIds(replaceOldId(info.getDependOnIds(), idMapper));
            }
        }
        //替换完成，查找根节点所有子节点集合
        //先排序将pid=0的在前
        save.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        getGroupedDescendantIds(save);
    }

    private String replaceOldId(String dependOnIds, Map<Long, Long> idMapper) {
        StringJoiner joiner = new StringJoiner(",");
        for (String oldId : dependOnIds.split(",")) {
            joiner.add(idMapper.get(Long.parseLong(oldId)) + "");
        }
        return joiner.toString();
    }


    // 方法用于获取所有顶级节点（pid=0）及其子孙节点的ID，并按顶级节点分组
    public static void getGroupedDescendantIds(List<OpsTaskGenInfo> tasks) {
        Map<Long, OpsTaskGenInfo> taskMap = tasks.stream().collect(Collectors.toMap(OpsTaskGenInfo::getId, task -> task));
        for (OpsTaskGenInfo task : tasks) {
            //追加所有子节点id
            List<Long> childDes = getAllDescendantIds(task, tasks);
            if (!childDes.isEmpty()) {
                StringJoiner joiner = new StringJoiner(",");
                for (Long l : childDes) {
                    joiner.add(l + "");
                }
                task.setTaskChildIds(joiner.toString());
            }
            //追加所有父节点id
            task.setParentIds(getParentNodesInMemory(task.getId(), taskMap));
        }
        //同层级任务追加唯一id
        assignUnifiedIdToSiblingsInMemory(tasks);
    }


    /**
     * 给同层级的任务节点加一个统一ID（在内存中操作）
     *
     * @param tasks 任务列表
     */
    public static void assignUnifiedIdToSiblingsInMemory(List<OpsTaskGenInfo> tasks) {
        // 构建任务节点的映射
        Map<Long, List<OpsTaskGenInfo>> siblingsMap = new HashMap<>();
        for (OpsTaskGenInfo task : tasks) {
            siblingsMap.computeIfAbsent(task.getParentId(), k -> new ArrayList<>()).add(task);
        }
        for (List<OpsTaskGenInfo> siblings : siblingsMap.values()) {
            long unifiedId = IdWorker.getId();
            for (OpsTaskGenInfo sibling : siblings) {
                sibling.setFloorId(Long.toString(unifiedId));
            }
        }
    }

    /**
     * 获取任务节点的所有父级节点（在内存中操作）
     *
     * @param taskId 任务ID
     * @return 父级节点列表
     */
    public static String getParentNodesInMemory(Long taskId, Map<Long, OpsTaskGenInfo> taskMap) {
        // 构建任务节点的映射
        List<String> parentNodes = new ArrayList<>();
        OpsTaskGenInfo currentTask = taskMap.get(taskId);
        while (currentTask != null && currentTask.getParentId() != 0L) {
            OpsTaskGenInfo parent = taskMap.get(currentTask.getParentId());
            if (parent != null) {
                parentNodes.add(parent.getId() + "");
                currentTask = parent;
            } else {
                break;
            }
        }
        return parentNodes.isEmpty() ? "0" : String.join(",", parentNodes);
    }

    static List<Long> getAllDescendantIds(OpsTaskGenInfo info, List<OpsTaskGenInfo> tasks) {
        List<Long> ids = new ArrayList<>();
        for (OpsTaskGenInfo child : tasks) {
            if (child.getParentId().equals(info.getId())) {
                ids.add(child.getId());
                ids.addAll(getAllDescendantIds(child, tasks));
            }
        }
        return ids;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void schedulerProcessByDynamic(Long taskId, String taskType) {
        //自定义执行的任务考虑,重复周期,触发器只管采集结果，所以任务要去判定是否在允许重复的周期内已经执行过一次了，当前场景默认当日
        if (dynamicLogService.checkTaskIsRecord(taskId)) {
            log.error("日常任务-执行失败,该任务已经在当天执行过");
            return;
        }
        //单个任务单元生成任务
        if (Objects.equals(taskType, "1")) {
            OpsTaskAttrBasic basic = opsTaskAttrBasicService.getById(taskId);
            if (Objects.isNull(basic)) {
                log.error("dynamic-scheduler任务执行失败,未找到配置基类{}", taskId);
                return;
            }
            if (!StringUtils.hasText(basic.getTaskTriggerId())) {
                log.error("dynamic-scheduler任务执行失败,未找到触发器配置");
                return;
            }
            TaskScriptResultVO ruleEnd = tradeTypeService.checkDateType(basic.getTaskTriggerId());
            if (!ruleEnd.getStatus()) {
                log.error("dynamic-scheduler任务生成异常,触发器规则执行结果为false 任务单元id={}", basic.getId());
            }
            //如果不为false,则需要根据任务类型判定是否已经生成过该任务
            OpsTaskGenInfo info = convertGenInfo(basic);
            save(info);
            dynamicLogService.insertSuccessTaskRecord(taskId);
            log.info("dynamic-scheduler 自定义-通过任务单元-生成任务清单完成{}", taskId);
        }
        //通过模板生成任务
        if (Objects.equals(taskType, "2")) {
            OpsTaskTemplate template = taskTemplateService.getById(taskId);
            if (Objects.isNull(template)) {
                log.error("dynamic-scheduler任务执行失败,未找到配置基类{}", taskId);
                return;
            }
            if (!StringUtils.hasText(template.getTriggerId())) {
                log.error("dynamic-scheduler任务执行失败,未找到触发器配置");
                return;
            }
            TaskScriptResultVO ruleEnd = tradeTypeService.checkDateType(template.getTriggerId());
            if (!ruleEnd.getStatus()) {
                log.error("dynamic-scheduler任务生成异常,触发器规则执行结果为false 模板id={}", template.getId());
            }
            List<String> ids = taskTemplateService.listReplicaIds(taskId);
            LambdaQueryWrapper<OpsTaskAttrBasicReplica> basics = new LambdaQueryWrapper<>();
            basics.in(OpsTaskAttrBasicReplica::getId, ids);
            List<OpsTaskAttrBasicReplica> arr = replicaService.list(basics);
            List<OpsTaskGenInfo> save = arr.stream().map(this::convertGenInfo).collect(Collectors.toList());
            replaceIdAndFillChildIdsAndSort(save);
            saveBatch(save);
            dynamicLogService.insertSuccessTaskRecord(taskId);
            log.info("dynamic-template-scheduler生成任务清单数量{}", save.size());
        }
    }

    /**
     * 任务清单查询，分页情况下，最顶层是根节点，有子级节点需要二次查询并写入
     *
     * @param p_child
     * @param condition
     * @return
     */
    @Override
    public Map<Long, List<OpsTaskGenInfo>> findChild(Map<Long, String> p_child, ConditionTaskDTO condition) {
        List<String> longs = new ArrayList<>();
        //取所有子节点id
        p_child.forEach((k, v) -> {
            longs.addAll(List.of(v.split(",")));
        });
        //查询数据
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OpsTaskGenInfo::getId, longs);
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        List<OpsTaskGenInfo> childMeta = baseMapper.findChildDetailByAuth(queryWrapper, condition);
        //取id与实体对象做映射
        Map<Long, OpsTaskGenInfo> nodeMap = new HashMap<>();
        for (OpsTaskGenInfo node : childMeta) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }
        //进行树状生成
        childMeta.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        Map<Long, List<OpsTaskGenInfo>> res = new HashMap<>();
        p_child.forEach((k, v) -> {
            List<OpsTaskGenInfo> cap = new ArrayList<>();
            //由于是使用map生成数，且有引用问题,导致根多选
            treeBuilder(childMeta, nodeMap, cap, k);
            res.put(k, cap);
        });
        return res;
    }


    @Override
    public Map<Long, List<OpsTaskGenInfo>> findChildByLeader(Map<Long, String> p_child, ConditionTaskDTO condition) {
        List<String> longs = new ArrayList<>();
        //取所有子节点id
        p_child.forEach((k, v) -> {
            longs.addAll(List.of(v.split(",")));
        });
        //查询数据
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OpsTaskGenInfo::getId, longs);
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        queryWrapper.eq(OpsTaskGenInfo::getTaskPriority, 1);
        List<OpsTaskGenInfo> childMeta = baseMapper.findChildDetailByAuth(queryWrapper, condition);
        //取id与实体对象做映射
        Map<Long, OpsTaskGenInfo> nodeMap = new HashMap<>();
        for (OpsTaskGenInfo node : childMeta) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }
        //进行树状生成
        childMeta.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        Map<Long, List<OpsTaskGenInfo>> res = new HashMap<>();
        p_child.forEach((k, v) -> {
            List<OpsTaskGenInfo> cap = new ArrayList<>();
            //由于是使用map生成数，且有引用问题,导致根多选
            treeBuilder(childMeta, nodeMap, cap, k);
            res.put(k, cap);
        });
        return res;
    }

    @Override
    public Map<Long, List<OpsTaskGenInfo>> findChildForAudit(Map<Long, String> p_child) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTaskByTemplate(TemplateVO vo, Boolean up) {

        //先删除一次job_relation的关系数据
        deleteTemplateJobRelation(vo.getTemplate().getId());
        //更新一遍模板关联的replica的数据，包括树状关系？与个体内容？
        if (up) {
            taskTemplateService.saveOrUpdateAll(vo);
        }
        //根据类型判定是 立即生成/日常 / 特殊规则日 / 按需根据模板内的所有单个任务本身配置 来完成？
        String tempType = vo.getTemplate().getSchedulerType();
        //手动，则任务立即生成
        List<OpsTaskAttrBasicReplica> flattens = new ArrayList<>();
        flattenHandlerSelf(vo.getList(), flattens);

        if (Objects.equals(TaskConstant.MANUAL, tempType)) {
            List<OpsTaskGenInfo> saveBatch = flattens.stream().map(this::convertGenInfo).collect(Collectors.toList());
            replaceIdAndFillChildIdsAndSort(saveBatch);
            saveBatch(saveBatch);
        }
        //日常任务,工作日的
        if (Objects.equals(TaskConstant.TASK_TYPE_DAILY, tempType)) {
            List<OpsTaskJobRelation> relations = new ArrayList<>();
            for (OpsTaskAttrBasicReplica replica : flattens) {
                OpsTaskJobRelation item = new OpsTaskJobRelation();
                item.setTaskType("2");
                item.setJobId("1");
                item.setTaskId(String.valueOf(replica.getId()));
                relations.add(item);
            }
            relationService.saveBatch(relations);
        }
        //自定义的，需要选择一个触发器去执行,并且由于是模板所以关联n个单元配置对象
        //废弃
        if (Objects.equals(TaskConstant.TASK_TYPE_DYNAMIC, tempType)) {
            //只保存模板任务
            saveSchedulerPlanByTemplate(vo.getTemplate(), vo.getTemplate().getId());
        }

    }

    private void deleteTemplateJobRelation(String id) {
        relationService.deleteDailyTaskForTemplateId(id);
    }

    @Override
    public IPage<OpsTaskGenInfo> pageCustom(IPage<OpsTaskGenInfo> page,
                                            Wrapper<OpsTaskGenInfo> wrapper,
                                            ConditionTaskDTO dto) {
        return baseMapper.pageCustom(page, wrapper, dto);
    }

    @Override
    public List<OpsTaskGenInfo> dashboardListTask(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTask(wrapper, condition);
    }

    @Override
    public List<OpsTaskGenInfo> dashboardListTaskDetail(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskDetail(wrapper, condition);
    }

    @Override
    public List<Long> dashboardListTaskCount(Wrapper<OpsTaskGenInfo> wrapper, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskCount(wrapper, condition);
    }

    @Override
    public IPage<OpsTaskGenInfo> pageCustomAudit(IPage<OpsTaskGenInfo> pageEntity,
                                                 LambdaQueryWrapper<OpsTaskGenInfo> wrapper,
                                                 ConditionTaskDTO condition) {


        return baseMapper.pageCustomAudit(pageEntity, wrapper, condition);
    }

    @Override
    public List<OpsTaskGenInfo> listCustomAudit(LambdaQueryWrapper<OpsTaskGenInfo> wrapper,
                                                ConditionTaskDTO condition) {
        List<OpsTaskGenInfo> res = baseMapper.listCustomAudit(wrapper, condition);
        //排序生成树状
        res.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        //treeBuilder
        Map<Long, OpsTaskGenInfo> mapper = res.stream().collect(Collectors.toMap(OpsTaskGenInfo::getId, i -> i));
        List<Long> forOrgTree = new ArrayList<>();
        List<OpsTaskGenInfo> treeInfos = treeBuilder(res, mapper, forOrgTree);
        List<OpsTaskGenInfo> dv = res.stream().filter(i -> !forOrgTree.contains(i.getId())).collect(Collectors.toList());
        treeInfos.addAll(dv);
        return treeInfos;
    }

    private List<OpsTaskGenInfo> treeBuilder(List<OpsTaskGenInfo> res, Map<Long, OpsTaskGenInfo> cap, List<Long> forOrgTree) {
        List<OpsTaskGenInfo> fin = new ArrayList<>();
        for (OpsTaskGenInfo re : res) {
            if (re.getParentId() == 0L) {
                fin.add(re);
                forOrgTree.add(re.getId());
            } else {
                OpsTaskGenInfo p = cap.get(re.getParentId());
                if (p != null) {
                    if (p.getChildren() == null) {
                        p.setChildren(new ArrayList<>());
                    }
                    p.getChildren().add(re);
                    forOrgTree.add(re.getId());
                }
            }
        }
        return fin;
    }

    @Override
    public Map<Long, List<OpsTaskGenInfo>> findChildByAudit(Map<Long, String> p_child, ConditionTaskDTO condition) {
        List<String> longs = new ArrayList<>();
        //取所有子节点id
        p_child.forEach((k, v) -> {
            longs.addAll(List.of(v.split(",")));
        });
        //查询数据
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OpsTaskGenInfo::getId, longs);
        List<OpsTaskGenInfo> childMeta = baseMapper.findTasksForCheckRequire(queryWrapper, condition);
        //取id与实体对象做映射
        Map<Long, OpsTaskGenInfo> nodeMap = new HashMap<>();
        for (OpsTaskGenInfo node : childMeta) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }
        //进行树状生成
        childMeta.sort(Comparator.comparing(OpsTaskGenInfo::getParentId));
        Map<Long, List<OpsTaskGenInfo>> res = new HashMap<>();
        p_child.forEach((k, v) -> {
            List<OpsTaskGenInfo> cap = new ArrayList<>();
            //由于是使用map生成数，且有引用问题,导致根多选
            treeBuilder(childMeta, nodeMap, cap, k);
            res.put(k, cap);
        });
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferTask(TaskTransferVO transferVO) {
        if (!opsTaskTransferConfService.delayTransfer(transferVO)) {
            return;
        }
        //哪个岗位到哪个人
        //任务追加转派备注，与修改任务转派状态
        OpsTaskGenInfo info = getById(transferVO.getTaskId());
        //其次查看转派任务是子节点还是叶子节点,如果父节点不为空0,则需要置为0 ，不然无法被转派人查询
        if (info.getParentId() != 0L && transferVO.getTransferType().equals("1")) {
            info.setParentId(0L);
        }
        List<OpsTaskGenInfo> children = new ArrayList<>();
        //子级不为空，则一样需要进行转派
        if (StringUtils.hasText(info.getTaskChildIds())) {
            List<String> ids = List.of(info.getTaskChildIds().split(","));
            LambdaQueryWrapper<OpsTaskGenInfo> query = new LambdaQueryWrapper<>();
            query.in(OpsTaskGenInfo::getId, ids);
            children = list(query);
        }
        changeInfoAttr(info, transferVO);
        if (!children.isEmpty()) {
            for (OpsTaskGenInfo child : children) {
                changeInfoAttr(child, transferVO);
            }
        }
        children.add(info);
        updateBatchById(children);
        updateParentTaskProperties(children);
    }

    /**
     * 更新父级任务属性 taskIds 将转移后的id剔除  排除完成操作影响
     * 将当前需要转派的id进行 当前树状数据查询
     * 查询的结果再进行一次去重
     * 剩下的任务对自身ids属性值进行去重后更新
     *
     * @param children 当前需要转派的任务数组
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateParentTaskProperties(List<OpsTaskGenInfo> children) {
        //查询所有转派任务的模板id
        List<String> tempId = children.stream().map(OpsTaskGenInfo::getTaskBindTemplateId).distinct().collect(Collectors.toList());
        //获取当天的任务生成时间
        String time = children.get(0).getTaskGenTime();
        //获取转派的所有任务id
        List<String> tranIds = children.stream().map(i -> i.getId() + "").collect(Collectors.toList());
        //查询当前转派的任务所在模板的所有任务集合
        List<OpsTaskGenInfo> todayInfo = list(new LambdaQueryWrapper<OpsTaskGenInfo>().in(OpsTaskGenInfo::getTaskBindTemplateId, tempId).
                eq(OpsTaskGenInfo::getTaskGenTime, time));
        //需要剔除的父任务
        List<OpsTaskGenInfo> opsTaskGenInfoList = new ArrayList<>();
        for (OpsTaskGenInfo item : todayInfo) {
            //查询是否包含此id 的父任务且不在今天转派任务内的任务
            if (item.getTaskChildIds() != null && !tranIds.contains(item.getId() + "")) {
                //剔除掉当前转派的任务id
                List<String> ids = new ArrayList<>(List.of(item.getTaskChildIds().split(",")));
                if (ids.removeAll(tranIds)) {
                    //生成新的子任务ids集合
                    item.setTaskChildIds(String.join(",", ids));
                    opsTaskGenInfoList.add(item);
                }
            }
        }
        updateBatchById(opsTaskGenInfoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchTransferTask(TaskTransferVO transferVO) {
        if (!opsTaskTransferConfService.delayTransfer(transferVO)) {
            return;
        }
        List<OpsTaskGenInfo> opsTaskGenInfoList = listByIds(transferVO.getTaskIds());
        for (OpsTaskGenInfo opsTaskGenInfoItem : opsTaskGenInfoList) {
            if (opsTaskGenInfoItem.getParentId() != 0L && !opsTaskGenInfoList.stream().anyMatch(e -> e.getId().equals(opsTaskGenInfoItem.getParentId()))) {
                opsTaskGenInfoItem.setParentId(0L);
            }
            changeInfoAttr(opsTaskGenInfoItem, transferVO);
        }
        updateBatchById(opsTaskGenInfoList);
        updateParentTaskProperties(opsTaskGenInfoList);
    }

    private void changeInfoAttr(OpsTaskGenInfo info, TaskTransferVO transferVO) {
        if (transferVO.getType().equals("1")) {
            if (transferVO.getTransferType().equals("1")) {
                info.setTaskOwnerType("1");
                info.setTaskOwnerId(transferVO.getOrgId());
                info.setTaskOwnerVal(transferVO.getOrgName());
                info.setTaskTransferDesc(transferVO.getTaskDesc());
                info.setOwnerOrgId(transferVO.getOrgId());
                info.setTaskTransferStatus(1);
            } else {
                info.setTaskCheckTransferStatus(1);
                info.setTaskCheckType("1");
                info.setTaskCheckId(transferVO.getOrgId());
                info.setTaskCheckVal(transferVO.getOrgName());
            }
            info.setTaskTransferUserId(SecureUtil.currentUserId());
        }
        if (transferVO.getType().equals("2")) {
            if (transferVO.getTransferType().equals("1")) {
                info.setTaskOwnerType("2");
                info.setTaskOwnerId(transferVO.getUserId());
                info.setTaskOwnerVal(transferVO.getUserName());
                info.setTaskTransferDesc(transferVO.getTaskDesc());
                info.setTaskTransferStatus(1);
                info.setOwnerOrgId(transferVO.getOrgId());
            } else {
                info.setTaskCheckTransferStatus(1);
                info.setTaskCheckType("2");
                info.setTaskCheckId(transferVO.getUserId());
                info.setTaskCheckVal(transferVO.getUserName());
            }
            info.setTaskTransferUserId(SecureUtil.currentUserId());
        }
    }


    /**
     * 任务置完成
     *
     * @param vo 任务id
     * @return 1 附件未上传
     * 2 关联任务未完成
     * 3 正常完成
     * 4 有子节点批量完成不允许
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String taskComplete(TaskCompleteVO vo) {
        OpsTaskGenInfo info = getById(vo.getTaskId());
        info.setTaskCompleteDesc(vo.getTaskDesc());
        info.setDelayState(isTimeDelay(info));
        info.setWorkAmount(vo.getWorkAmount());
        //如果是非叶子节点
        if (StringUtils.hasText(info.getTaskChildIds())) {
            if (StringUtils.hasText(info.getRequiredItem()) &&
                    JSONUtil.toBean(info.getRequiredItem(), RequireOptionDTO.class).getBatchCompletion() == 1) {
                return "4";
            }
            return batchTaskComplete(info, vo.getRootId());
        }
        return singleTaskComplete(info, vo.getTaskId(), vo.getRootId(), false);
    }

    private String isTimeDelay(OpsTaskGenInfo info) {
        if (ObjUtil.isEmpty(info.getTaskEndTime())) {
            return "0";
        }
        if (DateUtil.isSameDay(info.getTaskEndTime(), new Date())) {
            return "0";
        }
        if (DateUtil.compare(info.getTaskEndTime(), new Date()) < 0) {
            return "1";
        }
        return "0";
    }

    /**
     * 任务置为未发生状态
     *
     * @param vo 任务id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskNoExist(TaskCompleteVO vo) {
        OpsTaskGenInfo info = getById(vo.getTaskId());
        info.setTaskCompleteDesc(vo.getTaskDesc());
        //如果是非叶子节点
        if (StringUtils.hasText(info.getTaskChildIds())) {
            batchTaskSetNoExist(info);
        }
        singleTaskSetNoExist(info, vo.getTaskId(), vo.getRootId());
    }


    private String batchTaskComplete(OpsTaskGenInfo info, String rootId) {
        //先检查当前节点任务是否满足点击完成操作
        String targetStatus = singleTaskComplete(info, String.valueOf(info.getId()), rootId, true);
        if (!targetStatus.equals("3")) {
            return targetStatus;
        }
        //先查出所有子节点，短内容
        List<String> ids = new ArrayList<>(List.of(info.getTaskChildIds().split(",")));
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getId,
                OpsTaskGenInfo::getParentId,
                OpsTaskGenInfo::getTaskName,
                OpsTaskGenInfo::getDependOnIds,
                OpsTaskGenInfo::getRequiredItem,
                OpsTaskGenInfo::getTaskCheckReq,
                OpsTaskGenInfo::getTaskAttachmentsType, OpsTaskGenInfo::getTaskOwnerType, OpsTaskGenInfo::getTaskOwnerId);
        queryWrapper.in(OpsTaskGenInfo::getId, ids);
        queryWrapper.in(OpsTaskGenInfo::getTaskCompleteStatus, 1);
        List<OpsTaskGenInfo> OpsTaskGenInfos = list(queryWrapper);
        boolean isLeader = !opsSysOrgService.list(Wrappers.lambdaQuery(OpsSysOrg.class).eq(OpsSysOrg::getLeader, SecureUtil.currentUserId())
                .ne(OpsSysOrg::getOrgType, "3").eq(OpsSysOrg::getDeleted, 0)).isEmpty();

        List<OpsTaskGenInfo> otherTasks = OpsTaskGenInfos.stream().filter(e -> !isLeader && !(e.getTaskOwnerType().equals("2") && e.getTaskOwnerId().equals(SecureUtil.currentUserId()))).collect(Collectors.toList());
        List<OpsTaskGenInfo> list = OpsTaskGenInfos.stream().filter(e -> isLeader || (e.getTaskOwnerType().equals("2") && e.getTaskOwnerId().equals(SecureUtil.currentUserId()))).collect(Collectors.toList());
        //然后查看各自节点的依赖节点是否超出当前所有节点范围
        //如果超出，则查询超出范围的节点状态是否是已完成，是则可以进行下一步判定
        Set<String> childDepends = new HashSet<>();
        for (OpsTaskGenInfo genInfo : list) {
            if (StringUtils.hasText(genInfo.getDependOnIds())) {
                childDepends.addAll(List.of(genInfo.getDependOnIds().split(",")));
            }
        }
        if (!childDepends.isEmpty()) {
            ids.add(info.getId() + "");
            Set<String> allNow = new HashSet<>(ids);
            childDepends.removeAll(allNow);
            //如果当前批量要点及完成任务所以依赖节点超出当前范围,则查看超出范围的节点完成状态
            if (!childDepends.isEmpty()) {
                //超出状态大于1,则不满足条件
                int countDepends = baseMapper.countDependStatus(new ArrayList<>(childDepends));
                if (countDepends > 0) {
                    return "2";
                }
            }
        }
        //查看是否有必填项校验，校验不通过则也无法全部完成
        for (OpsTaskGenInfo opsTaskGenInfo : list) {
            if (!validateAttachment(opsTaskGenInfo)) {
                return "1";
            }
        }
        //两项完成则进行更新操作
        List<Long> upFinalCom = new ArrayList<>();
        List<Long> upAuditCom = new ArrayList<>();
        for (OpsTaskGenInfo genInfo : list) {
            if (genInfo.getTaskCheckReq().equals("1")) {
                upAuditCom.add(genInfo.getId());
            } else {
                upFinalCom.add(genInfo.getId());
            }
        }
        //需要复核的任务完成状态为2 待复核
        if (!upAuditCom.isEmpty())
            baseMapper.updateTaskCompleteStatusByBatch(2, upAuditCom, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), info.getDelayState());
        //不需要复核的任务完成状态为3 已完成
        if (!upFinalCom.isEmpty())
            baseMapper.updateTaskCompleteStatusByBatch(3, upFinalCom, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), info.getDelayState());
        if (otherTasks.isEmpty()) {
            singleTaskComplete(info, String.valueOf(info.getId()), rootId, false);
            asyncService.updateTaskOperationInfo(info, 2, SecureUtil.currentUserId());
        }

        return "3";
    }

    private void batchTaskSetNoExist(OpsTaskGenInfo info) {
        //先查出所有子节点，短内容
        List<String> ids = new ArrayList<>(List.of(info.getTaskChildIds().split(",")));
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getId,
                OpsTaskGenInfo::getParentId,
                OpsTaskGenInfo::getTaskName,
                OpsTaskGenInfo::getDependOnIds,
                OpsTaskGenInfo::getRequiredItem,
                OpsTaskGenInfo::getTaskCheckReq,
                OpsTaskGenInfo::getTaskAttachmentsType);
        queryWrapper.in(OpsTaskGenInfo::getId, ids);
        List<OpsTaskGenInfo> list = list(queryWrapper);
        //两项完成则进行更新操作
        List<Long> upFinalCom = new ArrayList<>();
        for (OpsTaskGenInfo genInfo : list) {
            upFinalCom.add(genInfo.getId());
        }
        //不需要复核的任务完成状态为5 未发生
        if (!upFinalCom.isEmpty())
            baseMapper.updateTaskCompleteStatusByBatch(5, upFinalCom, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), info.getDelayState());
    }

    public String singleTaskComplete(OpsTaskGenInfo info, String taskId, String rootId, Boolean onlyVerify) {
        if (StringUtils.hasText(info.getTaskAttachmentsType()) && !validateAttachment(info)) {
            return "1";
        }
        if (StringUtils.hasText(info.getDependOnIds()) && !validateDepends(info)) {
            return "2";
        }
        if (onlyVerify) return "3";
        int status = 3;
        if (info.getTaskCheckReq() != null && info.getTaskCheckReq().equals("1")) {
            status = 2;
        }
        //叶子节点需要查看所在分支节点除去本身外是否已全部已完成,如果是，则需要把pid节点调整也置为已完成或待复核
//         checkOtherLeafIncludeStatusAndProcess(taskId, info.getParentId(), status, info.getDelayState());
        if (StringUtils.hasText(info.getTaskChildIds())) {
            LambdaQueryWrapper<OpsTaskGenInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OpsTaskGenInfo::getId, new ArrayList<>(List.of(info.getTaskChildIds().split(","))));
            List<OpsTaskGenInfo> opsTaskGenInfoList = baseMapper.selectList(wrapper);
            if (opsTaskGenInfoList.stream().allMatch(task -> task.getTaskCompleteStatus().equals(3) || task.getTaskCompleteStatus().equals(5))) {
                status = 3;
            } else {
                status = 1;
            }
        }
        baseMapper.updateTaskCompleteStatus(status, taskId, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), info.getDelayState(), info.getWorkAmount());
        correctParentStatus(rootId, status);
        asyncService.updateTaskOperationInfo(info, 1, SecureUtil.currentUserId());

        return "3";
    }


    private void correctParentStatus(String rootId, int status) {
        if (StringUtils.hasText(rootId)) {
            OpsTaskGenInfo opsTaskGenInfo = getById(rootId);
            LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(OpsTaskGenInfo::getId, Arrays.stream(opsTaskGenInfo.getTaskChildIds().split(",")).collect(Collectors.toList()));
            queryWrapper.orderByDesc(OpsTaskGenInfo::getTaskSort);
            List<OpsTaskGenInfo> OpsTaskGenInfoList = baseMapper.selectList(queryWrapper);
            OpsTaskGenInfoList.add(opsTaskGenInfo);
            for (OpsTaskGenInfo opsTaskGenInfoItem : OpsTaskGenInfoList) {
                if (opsTaskGenInfoItem.getTaskChildIds() != null) {
                    opsTaskGenInfoItem.setTaskChildIds(Arrays.stream(opsTaskGenInfoItem.getTaskChildIds()
                            .split(",")).filter(e -> OpsTaskGenInfoList.stream().anyMatch(task -> task.getId().equals(Long.valueOf(e))) && OpsTaskGenInfoList.stream().filter(task -> task.getId().equals(Long.valueOf(e))).findFirst().get().getParentId() != 0L).collect(Collectors.joining(",")));
                }
                List<OpsTaskGenInfo> opsTaskGenInfoFilter = OpsTaskGenInfoList.stream().filter(task -> opsTaskGenInfoItem.getTaskChildIds() != null &&
                                Arrays.stream(opsTaskGenInfoItem.getTaskChildIds()
                                                .split(","))
                                        .collect(Collectors.toList())
                                        .contains(task.getId().toString()))
                        .collect(Collectors.toList());
                if (!opsTaskGenInfoFilter.isEmpty() && ((status == 5 && opsTaskGenInfoFilter.stream().allMatch(task -> task.getTaskCompleteStatus() == status || task.getTaskCompleteStatus() == 3))
                        || (status != 3 && opsTaskGenInfoFilter.stream().allMatch(task -> task.getTaskCompleteStatus() == status))
                        || (status == 3 && opsTaskGenInfoFilter.stream().allMatch(task -> task.getTaskCompleteStatus() == status || task.getTaskCompleteStatus() == 5)))) {
                    if (opsTaskGenInfoItem.getTaskCheckReq() != null && opsTaskGenInfoItem.getTaskCheckReq().equals("1") && status == 3) {
                        TaskCheckVO taskCheckVO = new TaskCheckVO();
                        changeCheckTaskInfo(opsTaskGenInfoItem, taskCheckVO);
                    } else {
                        if (status == 3) {
                            opsTaskGenInfoItem.setCompleteTime(new Date());
                        }
                        if (status == 5 && opsTaskGenInfoFilter.stream().anyMatch(task -> task.getTaskCompleteStatus() == 3)) {
                            opsTaskGenInfoItem.setTaskCompleteStatus(3);
                        } else {
                            opsTaskGenInfoItem.setTaskCompleteStatus(status);
                        }
                    }

                }
            }
            updateBatchById(OpsTaskGenInfoList);
        }
    }

    private void singleTaskSetNoExist(OpsTaskGenInfo info, String taskId, String rootId) {

        baseMapper.updateTaskCompleteStatus(5, taskId, new Date(), SecureUtil.currentUserId(), info.getTaskCompleteDesc(), null, null);
        correctParentStatus(rootId, 5);
    }

    /**
     * 查询当前叶子任务所在的分支中，是否其他任务都已经全部已完成
     * 如果其他任务已经全部完成，且当前任务也是操作为已完成状态，则同步更新分支节点任务状态为 已完成
     * 如果当前任务状态为待复核，则不做后续执行操作
     * 如果当情人任务为已完成操，但是其他任务中有未完成或者待复核任务，则同样不做后续操作
     *
     * @param taskId     当前任务id
     * @param parentId   当前任务的父id
     * @param status     当前任务的操作状态
     * @param delayState 是否为延期完成
     */
    private void
    checkOtherLeafIncludeStatusAndProcess(String taskId, Long parentId, int status, String delayState) {
        if (parentId == 0L || status == 2) {
            //不做操作
            return;
        }
        //查询所有同级节点状态
        long count = baseMapper.taskGenInfoFindLeafAllStatus(taskId, parentId);
        if (count > 0) {
            return;
        }
        baseMapper.updateTaskCompleteStatus(status, String.valueOf(parentId), new Date(), SecureUtil.currentUserId(), "", delayState, null);
    }

    /**
     * 校验关联任务状态是否已完成
     *
     * @param info 任务清单
     * @return bool
     */
    private boolean validateDepends(OpsTaskGenInfo info) {
        List<String> depIds = List.of(info.getDependOnIds().split(","));
        if (depIds.isEmpty()) {
            return true;
        }
        if (depIds.contains("null")) {
            return true;
        }
        int countUnComplete = baseMapper.countDependStatus(depIds);
        return countUnComplete == 0;
    }

    /**
     * 校验附件是否上传
     *
     * @param info 当前信息
     * @return bool
     */
    private boolean validateAttachment(OpsTaskGenInfo info) {
        if (info.getTaskAttachmentsType().equals("0")) {
            return true;
        }
        long count = opsTaskGenInfoFileService.count(Wrappers.lambdaQuery(OpsTaskGenInfoFile.class).eq(OpsTaskGenInfoFile::getInfoId, info.getId()));
        return count > 0;
    }

    private void flattenHandlerSelf(List<OpsTaskAttrBasicReplica> list, List<OpsTaskAttrBasicReplica> flattens) {
        for (OpsTaskAttrBasicReplica item : list) {
            if (!item.getChild().isEmpty()) {
                flattens.add(item);
                flattenHandlerSelf(item.getChild(), flattens);
            } else {
                flattens.add(item);
            }
        }
    }

    public List<OpsTaskGenInfo> buildTree(List<OpsTaskGenInfo> nodes, Map<Long, OpsTaskGenInfo> nodeMap, Long pid) {
        List<OpsTaskGenInfo> tree = new ArrayList<>();
        for (OpsTaskGenInfo node : nodes) {
            if (node.getParentId().equals(pid)) {
                tree.add(node);
            } else {
                OpsTaskGenInfo parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }
        return tree;
    }

    public void treeBuilder(List<OpsTaskGenInfo> nodes, Map<Long, OpsTaskGenInfo> nodeMap, List<OpsTaskGenInfo> cap, Long pid) {
        for (OpsTaskGenInfo info : nodes) {
            Long parentId = info.getParentId();
            if (Objects.equals(parentId, pid)) {
                cap.add(info);
            } else {
                OpsTaskGenInfo pInfo = nodeMap.get(parentId);
                if (pInfo != null) {
                    if (pInfo.getChildren() == null) {
                        pInfo.setChildren(new ArrayList<>());
                    }
                    if (pInfo.getSetIds() == null) {
                        pInfo.setSetIds(new ArrayList<>());
                    }
                    if (!pInfo.getSetIds().contains(info.getId())) {
                        pInfo.getSetIds().add(info.getId());
                        pInfo.getChildren().add(info);
                    }
                }
            }
        }
    }

    /**
     * 任务单元生成任务列表
     *
     * @param opsTaskAttrBasic 任务单元实体类
     * @return this
     */
    @Override
    public OpsTaskGenInfo convertGenInfo(OpsTaskAttrBasic opsTaskAttrBasic) {
        OpsTaskGenInfo target = new OpsTaskGenInfo();
        BeanUtil.copyProperties(opsTaskAttrBasic, target);
        //id置空
        target.setId(null);
        //任务状态1正常
        target.setTaskProcessStatus(1);
        //任务进度1进行中
        target.setTaskCompleteStatus(1);
        //任务来源 1 任务单元
        target.setTaskRef(1);
        //任务转派 0 默认非转派任务
        target.setTaskTransferStatus(0);
        //任务默认为单节点任务
        target.setParentId(0L);
        //任务默认来源模板
        target.setTaskRefId(String.valueOf(opsTaskAttrBasic.getId()));
        if (target.getTaskAttachmentsType() == null) {
            target.setTaskAttachmentsType("0");
        }
        if (target.getTaskNameAppend() == 1 && target.getTaskAppendType() != 0) {
            target.setTaskName(appendName(target.getTaskName(), target.getTaskAppendType()));
        }
        //开始和截止时间,重置年月日,日常任务
        target.setTaskEndTime(hourMergeDate(target.getTaskEndTime(), target.getTaskTriggerType()));
        target.setTaskStartTime(hourMergeDate(target.getTaskStartTime(), target.getTaskTriggerType()));
        //周期任务，开始和结束时间与创建生成时间是不通的
        if (Objects.equals(opsTaskAttrBasic.getTaskType(), "period")) {
            if (opsTaskAttrBasic.getTaskStartThreshold() != 0) {
                try {
                    Date newStartTime = sysCalendarService.queryFeuDate(opsTaskAttrBasic.getTaskStartThreshold(), target.getTaskStartTime());
                    target.setTaskStartTime(newStartTime);
                } catch (TaskException e) {
                    log.error("周期性任务生成失败,工作日获取失败，任务id={}", target.getId(), e);
                }
            }
            if (opsTaskAttrBasic.getTaskEndThreshold() != 0) {
                try {
                    Date newEndTime = sysCalendarService.queryFeuDate(opsTaskAttrBasic.getTaskEndThreshold(), target.getTaskEndTime());
                    target.setTaskEndTime(newEndTime);
                } catch (TaskException e) {
                    log.error("周期性任务生成失败,工作日获取失败，任务id={}", target.getId(), e);
                }
            }
        }
        target.setTaskGenTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
        target.setCreateTime(null);
        target.setUpdateTime(null);
        return target;
    }

    private String appendName(String taskName, int taskAppendType) {
        //年
        if (taskAppendType == 1) {
            taskName = ("【" + DateUtil.year(new Date()) + "年】" + taskName);
        }
        //季度
        if (taskAppendType == 2) {
            taskName = ("【" + DateUtil.format(new Date(), "yyyyy") + "年第" + DateUtil.quarter(new Date()) + "季度】" + taskName);
        }
        //月
        if (taskAppendType == 3) {
            taskName = ("【" + DateUtil.format(new Date(), "yyyy年MM月") + "】" + taskName);
        }
        //周
        if (taskAppendType == 4) {
            taskName = ("【" + DateUtil.format(new Date(), "yyyy年MM月") + "第" + DateUtil.weekOfMonth(new Date()) + "周】" + taskName);
        }
        return taskName;
    }

    /**
     * 更新重置时间
     *
     * @param date            截止时分
     * @param taskTriggerType
     * @return 新时间
     */
    private Date hourMergeDate(Date date, String taskTriggerType) {
        if (taskTriggerType != null && taskTriggerType.equals("manual")) {
            return date;
        }
        int hour = DateUtil.hour(date, true);
        int minute = DateUtil.minute(date);
        // 将 dateB 的年月日部分提取出来，秒默认为0
        Date dateBWithTime = DateUtil.beginOfDay(new Date()); // 设置为当天的 00:00:00
        dateBWithTime = DateUtil.offsetMinute(dateBWithTime, hour * 60 + minute);
        return dateBWithTime;
    }

    /**
     * 任务单元生成任务列表
     *
     * @param opsTaskAttrBasic 任务单元实体类
     * @return this
     */
    @Override
    public OpsTaskGenInfo convertGenInfoByTemp(OpsTaskAttrBasic opsTaskAttrBasic) {
        OpsTaskGenInfo target = new OpsTaskGenInfo();
        BeanUtil.copyProperties(opsTaskAttrBasic, target);
        //id置空
        target.setId(null);
        //任务状态1正常
        target.setTaskProcessStatus(1);
        //任务进度1进行中
        target.setTaskCompleteStatus(1);
        //任务来源 1 手动
        target.setTaskRef(1);
        //任务转派 0 默认非转派任务
        target.setTaskTransferStatus(0);
        //任务默认为单节点任务
        target.setParentId(0L);
        target.setTaskType("temp");
        if (target.getTaskAttachmentsType() == null) {
            target.setTaskAttachmentsType("0");
        }
        target.setTaskGenTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
        target.setCreateTime(null);
        target.setUpdateTime(null);
        return target;
    }


    /***
     * 任务清单复核通过
     * @param condition  条件内容
     * @param auditVo 复核通过内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reviewTaskProcess(ConditionTaskDTO condition, TaskCheckVO auditVo) {
        //查询条件中岗位postId集合，检查岗位与任务信息是否匹配
        OpsTaskGenInfo pEn = getById(auditVo.getTaskId());
        List<OpsTaskGenInfo> children = new ArrayList<>();
        //如果有子集，则需要一块复核
        if (StringUtils.hasText(pEn.getTaskChildIds())) {
            List<String> ids = List.of(pEn.getTaskChildIds().split(","));
            LambdaQueryWrapper<OpsTaskGenInfo> query = new LambdaQueryWrapper<>();
            query.in(OpsTaskGenInfo::getId, ids);
            children = list(query);
        }
        children.add(pEn);
        for (OpsTaskGenInfo info : children) {
            changeCheckTaskInfo(info, auditVo);
        }
        updateBatchById(children);
        correctCheck(auditVo.getTaskId());
        asyncService.updateOperationCheckInfo(children, SecureUtil.currentUserId());
    }


    private void correctCheck(String taskId) {
        OpsTaskGenInfo opsTaskGenInfo = getById(taskId);
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskGenInfo::getTaskBindTemplateId, opsTaskGenInfo.getTaskBindTemplateId());
        queryWrapper.eq(OpsTaskGenInfo::getTaskGenTime, opsTaskGenInfo.getTaskGenTime());
        queryWrapper.eq(OpsTaskGenInfo::getParentId, "0");
        List<OpsTaskGenInfo> OpsTaskGenInfoList = baseMapper.selectList(queryWrapper);
        Optional<OpsTaskGenInfo> opsTaskGenInfoRoot = OpsTaskGenInfoList.stream()
                .filter(task -> task.getTaskChildIds() != null).
                filter(task -> Arrays.stream(task.getTaskChildIds().split(",")).collect(Collectors.toList()).contains(taskId)).findFirst();
        opsTaskGenInfoRoot.ifPresent(info -> correctParentStatus(info.getId().toString(), 3));
    }

    /**
     * 任务权限过滤
     */
    @Override
    public ConditionTaskDTO taskSpecialAuthFilter(String orgId) {
        //首先判定人员属性,部门负责人,还是岗位负责人,还是部门下的多岗位的合集-负责人
        //找出所有可见权限的岗位id与人员id
        //对数据进行过滤(传入岗位切换,有值辅助过滤)
        //特殊情况，如果pid不等于0且不是null的，则是中间节点，暂时不能剔除，或导致数据残缺
        //如果岗位id不为空需要追加岗位限定过滤
        ConditionTaskDTO dto = new ConditionTaskDTO();

        if (SecureUtil.isAdmin(null)) {
            if (StringUtils.hasText(orgId)) {
                dto.setType(2);
                dto.setPostIds(List.of(orgId));
                return dto;
            }
            dto.setType(4);
            dto.setPostIds(new ArrayList<>());
            return dto;
        }
        //org为空,则需要用户自身在组织中的权限进行范围查询
        //先查是否是部门负责人,如果是，把部门下所有岗位拿出来,查询
        //如果不是，则只查询个人owner与岗位类型的可见权限
        String userId = SecureUtil.currentUserId();
        OrgListDTO orgList = opsSysOrgService.allOrgIdByUser(userId);
        if (Objects.nonNull(orgList)) {
            //如果岗位信息为空则无权查看任何
            if (orgList.getOrgIds().isEmpty()) {
                return null;
            }
            //如果岗位组织id为空,则按照用户权限查看
            if (!StringUtils.hasText(orgId)) {
                dto.setPostIds(orgList.getOrgIds());
            } else {
                //否则以前端传入id为主
                dto.setPostIds(List.of(orgId));
            }
            //如果为领导,则类型调整为2
            if (orgList.getLeader()) {
                dto.setType(2);
                return dto;
            }
            //不为领导则类型调整为1
            dto.setType(1);
            //赋值用户id则
            dto.setUserId(userId);
            return dto;
        }
        return null;
    }

    /**
     * 定时任务，每日扫描允许自动延期，且任务是未完成状态的
     * 每天的下午 1点，3点，5点，10点进行扫描更改可延时的任务状态
     */
    @Override
    // @Scheduled(cron = "0 0 13,15,17,22 * * ?")  // 已迁移到调度中心，禁用原@Scheduled任务
    @Transactional(rollbackFor = Exception.class)
    public void timerEverDayScannerDelayTaskStatus() {
        log.info("延期任务扫描 - 开始");
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskGenInfo::getTaskGenTime, DateUtil.format(new Date(), "yyyy-MM-dd"));
        queryWrapper.eq(OpsTaskGenInfo::getTaskDeferredType, "1");
        queryWrapper.ne(OpsTaskGenInfo::getTaskCompleteStatus, "3");
        List<OpsTaskGenInfo> list = list(queryWrapper);
        Date now = new Date();
        if (list.stream().anyMatch(Objects::nonNull)) {
            List<Long> ids = new ArrayList<>();
            for (OpsTaskGenInfo info : list) {
                //如果当前任务还未到任务结束时间
                if (DateUtil.compare(info.getTaskEndTime(), now) <= 0) {
                    continue;
                }
                Date defferTime;
                try {
                    if (info.getTaskDeferredCount() == null || info.getTaskDeferredCount() == 0) {
                        log.error("任务延期更改时间操作异常,{} ,数据缺失延期阈值", info.getId());
                        continue;
                    }
                    defferTime = sysCalendarService.queryFeuDate(info.getTaskDeferredCount(), info.getTaskEndTime());
                } catch (TaskException e) {
                    log.error("任务延期更改时间操作异常,{}", info.getId(), e);
                    continue;
                }
                //时间更改查询时间
                ids.add(info.getId());
                LambdaUpdateWrapper<OpsTaskGenInfo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(OpsTaskGenInfo::getTaskEndTime, defferTime);
                updateWrapper.eq(OpsTaskGenInfo::getId, info.getId());
                update(updateWrapper);
            }
            log.info("延期任务扫描 - 命中 - {}", ids);
        }
        log.info("延期任务扫描 - 结束");
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboard(Date nowTime, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMulti(nowTime, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeaf(Date nowTime, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeaf(nowTime, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafByLeader(Date nowTime, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeafByLeader(nowTime, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetail(Date nowTime, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeafDetail(nowTime, genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardLeafDetailByLeader(Date nowTime, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiLeafDetailByLeader(nowTime, genTime, condition);
    }


    /**
     * 更改复核通过任务状态值
     *
     * @param pEn     对象
     * @param auditVo 通过对象
     */
    private void changeCheckTaskInfo(OpsTaskGenInfo pEn, TaskCheckVO auditVo) {
        //任务复核通过描述
        pEn.setTaskCheckDesc(auditVo.getTaskDesc());
        //任务复核状态置为1 通过
        pEn.setTaskCheckStatus("1");
        //任务状态置为完成
        pEn.setTaskCompleteStatus(3);
        //通过时间
        pEn.setAuditTime(new Date());
    }

    /**
     * 任务单元生成任务列表
     *
     * @param OpsTaskAttrBasicReplica 任务单元实体类副本-模板专用
     * @return this
     */
    @Override
    public OpsTaskGenInfo convertGenInfo(OpsTaskAttrBasicReplica OpsTaskAttrBasicReplica) {
        OpsTaskGenInfo target = new OpsTaskGenInfo();
        BeanUtil.copyProperties(OpsTaskAttrBasicReplica, target);
        //id置空
        //任务状态1正常
        target.setTaskProcessStatus(1);
        //任务进度1进行中
        target.setTaskCompleteStatus(1);
        //任务来源 1 任务单元
        target.setTaskRef(1);
        //任务转派 0 默认非转派任务
        target.setTaskTransferStatus(0);
        //任务配置来源
        target.setTaskRefId(OpsTaskAttrBasicReplica.getId());
        //重置任务开始和结束时间
        target.setTaskEndTime(hourMergeDate(target.getTaskEndTime(), target.getTaskTriggerType()));
        target.setTaskStartTime(hourMergeDate(target.getTaskStartTime(), target.getTaskTriggerType()));
        if (target.getTaskNameAppend() == 1 && target.getTaskAppendType() != 0) {
            target.setTaskName(appendName(target.getTaskName(), target.getTaskAppendType()));
        }
        //周期任务，开始和结束时间与创建生成时间是不同的
        if (Objects.equals(OpsTaskAttrBasicReplica.getTaskType(), "period")) {
            if (OpsTaskAttrBasicReplica.getTaskStartThreshold() != 0) {
                try {
                    Date newStartTime = sysCalendarService.queryFeuDate(OpsTaskAttrBasicReplica.getTaskStartThreshold(), target.getTaskStartTime());
                    target.setTaskStartTime(newStartTime);
                } catch (TaskException e) {
                    log.error("周期性任务生成失败,工作日获取失败，任务id={}", target.getId(), e);
                }
            }
            if (OpsTaskAttrBasicReplica.getTaskEndThreshold() != 0) {
                try {
                    Date newEndTime = sysCalendarService.queryFeuDate(OpsTaskAttrBasicReplica.getTaskEndThreshold(), target.getTaskEndTime());
                    target.setTaskEndTime(newEndTime);
                } catch (TaskException e) {
                    log.error("周期性任务生成失败,工作日获取失败，任务id={}", target.getId(), e);
                }
            }
        }
        target.setTaskGenTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
        target.setCreateTime(null);
        target.setUpdateTime(null);
        //任务默认为单节点任务
        return target;
    }

    @Override
    public void realDeleted(String format) {
        baseMapper.realDeleted(format);
    }

    /**
     * 批量复核动作
     *
     * @param condition 用户权限内容
     * @param checkVO   复核相关参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchReviewTaskProcess(ConditionTaskDTO condition, TaskCheckVO checkVO) {
        //对taskId进行去重?不用-搁置这个
        LambdaQueryWrapper<OpsTaskGenInfo> query = new LambdaQueryWrapper<>();
        query.in(OpsTaskGenInfo::getId, checkVO.getTaskIds());
        List<OpsTaskGenInfo> OpsTaskGenInfoLists = list(query);
        for (OpsTaskGenInfo opsTaskGenInfo : OpsTaskGenInfoLists) {
            changeCheckTaskInfo(opsTaskGenInfo, checkVO);
        }
        updateBatchById(OpsTaskGenInfoLists);
        // 查到当前选择任务集合 然后先找所有的孩子 置为完成  再向上找
        List<String> childIds = OpsTaskGenInfoLists.stream()
                .map(OpsTaskGenInfo::getTaskChildIds)
                .filter(Objects::nonNull)
                .flatMap(ids -> Arrays.stream(ids.split(",")))
                .collect(Collectors.toList());
        childIds.addAll(checkVO.getTaskIds());
        if (!childIds.isEmpty()) {
            LambdaUpdateWrapper<OpsTaskGenInfo> in = Wrappers.lambdaUpdate(OpsTaskGenInfo.class).set(OpsTaskGenInfo::getAuditTime, new Date()).set(OpsTaskGenInfo::getTaskCheckStatus, "1").set(OpsTaskGenInfo::getTaskCompleteStatus, 3).in(OpsTaskGenInfo::getId, childIds);
            update(in);
        }
        LambdaQueryWrapper<OpsTaskGenInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OpsTaskGenInfo::getTaskBindTemplateId, OpsTaskGenInfoLists.stream().map(OpsTaskGenInfo::getTaskBindTemplateId).collect(Collectors.toList()));
        lambdaQueryWrapper.eq(OpsTaskGenInfo::getTaskGenTime, OpsTaskGenInfoLists.get(0).getTaskGenTime());
        lambdaQueryWrapper.eq(OpsTaskGenInfo::getParentId, "0");
        List<OpsTaskGenInfo> opsTaskGenInfoRootList = list(lambdaQueryWrapper);
        LambdaQueryWrapper<OpsTaskGenInfo> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        List<String> ids = opsTaskGenInfoRootList.stream().flatMap(e -> Arrays.stream(e.getTaskChildIds() != null ? e.getTaskChildIds().split(",") : new String[]{})).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            lambdaQueryWrapper1.in(OpsTaskGenInfo::getId, ids);
            lambdaQueryWrapper1.orderByDesc(OpsTaskGenInfo::getTaskSort);
            List<OpsTaskGenInfo> opsTaskGenInfoList = list(lambdaQueryWrapper1);
            opsTaskGenInfoList = Stream.concat(opsTaskGenInfoList.stream(), opsTaskGenInfoRootList.stream()).collect(Collectors.toList());
            for (OpsTaskGenInfo opsTaskGenInfoItem : opsTaskGenInfoList) {
                if (opsTaskGenInfoItem.getTaskChildIds() != null && opsTaskGenInfoList.stream().filter(task -> Arrays.stream(opsTaskGenInfoItem.getTaskChildIds().split(",")).collect(Collectors.toList()).contains(task.getId() + "")).allMatch(task -> task.getTaskCompleteStatus() == 3)) {
                    TaskCheckVO taskCheckVO = new TaskCheckVO();
                    changeCheckTaskInfo(opsTaskGenInfoItem, taskCheckVO);
                }
            }
            updateBatchById(opsTaskGenInfoList);
        }

        asyncService.updateOperationBatchCheckInfo(childIds, SecureUtil.currentUserId());

    }


    /**
     * 插入定时任务待执行范围表
     *
     * @param info   任务单元配置信息
     * @param taskId 任务单元id
     */
    private void saveSchedulerPlanByUnit(OpsTaskGenInfo info, String taskId) {
        //日常任务只插入关联关系
//        if (TaskConstant.TASK_TYPE_DAILY.equals(info.getTaskTriggerType())) {
//            //插入定时任务表
//            jobHandler.createDailyJob(taskId);
//        }
//        //自定义任务一对一生成任务调度配置
//        if (TaskConstant.TASK_TYPE_DYNAMIC.equals(info.getTaskTriggerType())) {
//            try {
//                jobHandler.createDynamicJob(Long.valueOf(taskId), CronGeneric.generateCronExpression(Integer.parseInt(info.getTaskCronVal())), "1");
//            } catch (SchedulerException | TaskException e) {
//                throw new RuntimeException(e);
//            }
//        }
    }

    /**
     * 插入定时任务待执行范围表
     *
     * @param info       模板配置信息
     * @param templateId 模板id
     */
    private void saveSchedulerPlanByTemplate(OpsTaskTemplate info, String templateId) {
//        try {
//            jobHandler.createDynamicJob(Long.valueOf(templateId), CronGeneric.generateCronExpression(Integer.parseInt(info.getCronVal())), "2");
//        } catch (SchedulerException | TaskException e) {
//            throw new RuntimeException(e);
//        }
    }

    /**
     * 任务重置
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void taskReset(String id) {
        OpsTaskGenInfo opsTaskGenInfo = getById(id);
        if (StringUtils.hasText(opsTaskGenInfo.getTaskChildIds())) {
            LambdaUpdateWrapper<OpsTaskGenInfo> in = Wrappers.lambdaUpdate(OpsTaskGenInfo.class).set(OpsTaskGenInfo::getTaskCompleteStatus, 1).in(OpsTaskGenInfo::getId, Arrays.stream(opsTaskGenInfo.getTaskChildIds().split(",")).collect(Collectors.toList()));
            update(in);
        }
        LambdaQueryWrapper<OpsTaskGenInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(OpsTaskGenInfo::getTaskChildIds, id);
        lambdaQueryWrapper.eq(OpsTaskGenInfo::getTaskGenTime, opsTaskGenInfo.getTaskGenTime());
        lambdaQueryWrapper.eq(OpsTaskGenInfo::getTaskCompleteStatus, opsTaskGenInfo.getTaskCompleteStatus());
        List<OpsTaskGenInfo> opsTaskGenInfoList = list(lambdaQueryWrapper);
        opsTaskGenInfoList.add(opsTaskGenInfo);

        for (OpsTaskGenInfo opsTaskGenInfoItem : opsTaskGenInfoList) {
            opsTaskGenInfoItem.setTaskCompleteStatus(1);
        }
        updateBatchById(opsTaskGenInfoList);
        asyncService.updateOperationSetNull(opsTaskGenInfo, SecureUtil.currentUserId());
    }


    @Override
    public List<OpsTaskGenInfo> allTaskForNowDay(String date) {
        return baseMapper.allTaskForNowDay(date);
    }

    @Override
    public List<OpsTaskGenInfo> allTaskForNowDayV1(String date) {
        return baseMapper.allTaskForNowDayV1(date);
    }

    @Override
    public List<OpsTaskGenInfo> checkTaskForUser(String date, String userId) {
        return baseMapper.checkTaskForUser(date, userId);
    }

    /**
     * 统计指标详情列表相关
     *
     * @param date
     * @param userId
     * @return
     */
    @Override
    public List<OpsTaskGenInfo> checkTaskForUserDetail(String date, String userId) {
        return baseMapper.checkTaskForUserDetail(date, userId);
    }

    @Override
    public List<OpsTaskGenInfo> checkTaskListForLeaderOrUser(String genTime, ConditionTaskDTO condition) {
        //如果类型等于2 那么查询所有复核岗位id等于范围内的且复核完成的
        //如果不等于2 ，则认为最高权限查询所有
        //时间规则依然是，日常的只查当天的，周期的按照当天等于截止日期

        //普通员工
        if (condition.getType() != null && condition.getType() == 1) {
            //复核条目目前需要追加完成
            List<OpsTaskGenInfo> checkOpsTaskGenInfoList = checkTaskForUser(genTime, condition.getUserId());
            //过滤复核任务 - 日常任务则必须是今天的，  周期任务则结束时间是今天的才计入 完成和总数
            return checkOpsTaskGenInfoList.stream().filter(i -> i.getTaskCheckReq().equals("1") &&
                    (i.getTaskType().equals("daily") && i.getTaskGenTime().equals(genTime)) ||
                    ((i.getTaskType().equals("period") || i.getTaskType().equals("temp")) && DateUtil.format(i.getCompleteTime(), "yyyy-MM-dd").equals(genTime))).collect(Collectors.toList());
        }
        //领导直接查范围
        return baseMapper.checkTaskForLeader(genTime, condition);
    }

    @Override
    public List<OpsTaskGenInfo> checkTaskListForLeader(String genTime, ConditionTaskDTO condition) {
        //普通员工
        //领导直接查范围
        return baseMapper.checkTaskForLeaderByLevelHi(genTime, condition);
    }

    /**
     * 统计指标详情列表相关
     *
     * @param genTime
     * @param condition
     * @return
     */
    @Override
    public List<OpsTaskGenInfo> checkTaskListForLeaderOrUserDetail(String genTime, ConditionTaskDTO condition) {
        //如果类型等于2 那么查询所有复核岗位id等于范围内的且复核完成的
        //如果不等于2 ，则认为最高权限查询所有
        //时间规则依然是，日常的只查当天的，周期的按照当天等于截止日期

        //普通员工
        if (condition.getType() != null && condition.getType() == 1) {
            //复核条目目前需要追加完成和未完成
            List<OpsTaskGenInfo> checkOpsTaskGenInfoList = checkTaskForUserDetail(genTime, condition.getUserId());
            //过滤复核任务 - 日常任务则必须是今天的，  周期任务则结束时间是今天的才计入 完成和总数
            return checkOpsTaskGenInfoList.stream().filter(i -> i.getTaskCheckReq().equals("1") &&
                    (i.getTaskType().equals("daily") && i.getTaskGenTime().equals(genTime)) ||
                    ((i.getTaskType().equals("period") || i.getTaskType().equals("temp")) && DateUtil.format(i.getTaskEndTime(), "yyyy-MM-dd").equals(genTime))).collect(Collectors.toList());
        }
        //领导直接查范围
        return baseMapper.checkTaskForLeaderDetail(genTime, condition);
    }

    /**
     * 通过子节点id获取父节点数据
     *
     * @param ids
     * @return
     */
    @Override
    public List<OpsTaskGenInfo> getParentListByChildIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return baseMapper.getParentListByChildIds(ids);
    }

    @Override
    public List<OpsTaskGenInfo> dashboardTempListTaskLeaf(Wrapper<OpsTaskGenInfo> opsTaskGenInfoWrapper, ConditionTaskDTO condition) {
        return baseMapper.dashboardTempListTaskLeaf(opsTaskGenInfoWrapper, condition);
    }

    @Override
    public List<OpsTaskGenInfo> listByTemplateIdAndNowDate(String templateId, String nowDate) {
        return baseMapper.listByTemplateIdAndNowDate(templateId, nowDate);
    }

    @Override
    public List<OpsTaskGenInfo> multiTaskListForDashboardByLeader(Date now, String genTime, ConditionTaskDTO condition) {
        return baseMapper.dashboardListTaskByMultiByLeader(now, genTime, condition);
    }

    @Override
    public Map<String, Object> monthlyDetail(String dateMonthBegin, String dateMonthEnd, ConditionTaskDTO condition) {
        Map<String, Object> result = new HashMap<>();
        //交易日信息处理
        List<OpsSysCalendar> calendarList = calendarService.listByDateAllMarket(dateMonthBegin, dateMonthEnd);
        Map<String, JSONObject> tradeList = new HashMap<>();
        calendarList.forEach(calendar -> {
            if (tradeList.containsKey(calendar.getCalendarDate())) {
                JSONObject item = tradeList.get(calendar.getCalendarDate());
                item.getJSONArray("market").set(calendar.getMarket());
            } else {
                JSONObject item = new JSONObject();
                item.set("market", new JSONArray().set(calendar.getMarket()));
                item.set("date", calendar.getCalendarDate());
                tradeList.put(calendar.getCalendarDate(), item);
            }
        });
        result.put("tradeList", tradeList.values());
        //待办信息处理
        List<OpsTaskGenInfo> todoList = baseMapper.dashboardTaskListByTemp(
                DateUtil.parse(dateMonthBegin + " 00:00:00"), DateUtil.parse(dateMonthEnd + " 23:59:59"), condition
        );
        if (CollUtil.isEmpty(todoList)) {
            result.put("todoList", new JSONArray());
        } else {
            //排序
            result.put("todoList", todoList.stream().map(i->DateUtil.format(i.getTaskEndTime(), "yyyy-MM-dd")).distinct().collect(Collectors.toList()));
        }
        return result;
    }

}




