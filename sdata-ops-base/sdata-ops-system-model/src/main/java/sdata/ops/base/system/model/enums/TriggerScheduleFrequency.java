package sdata.ops.base.system.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 触发器调度频率枚举
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Getter
public enum TriggerScheduleFrequency {

    /**
     * 默认值/未设置
     */
    NONE(0, "none", "未设置", 0),

    /**
     * 30分钟
     */
    THIRTY_MINUTES(1, "30m", "30分钟", 30),

    /**
     * 1小时
     */
    ONE_HOUR(2, "1h", "1小时", 60),

    /**
     * 2小时
     */
    TWO_HOURS(3, "2h", "2小时", 120);

    /**
     * 数据库存储值
     */
    @EnumValue
    private final int value;

    /**
     * 频率代码
     */
    private final String code;

    /**
     * 频率描述
     */
    private final String description;

    /**
     * 间隔分钟数
     */
    private final int intervalMinutes;

    TriggerScheduleFrequency(int value, String code, String description, int intervalMinutes) {
        this.value = value;
        this.code = code;
        this.description = description;
        this.intervalMinutes = intervalMinutes;
    }
}
