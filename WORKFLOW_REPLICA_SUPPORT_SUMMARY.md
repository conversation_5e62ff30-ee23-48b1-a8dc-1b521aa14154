# 工作流触发器支持副本表功能实现总结

## 修改概述
为 `ops_task_attr_basic_replica` 表添加工作流触发器支持，使副本表中的任务单元也能配置和执行工作流触发器。

## 1. 数据库修改

### 新增字段脚本：`sql/add-workflow-fields-to-replica.sql`
```sql
-- 新增工作流ID字段
ALTER TABLE ops_task_attr_basic_replica 
ADD COLUMN workflow_id VARCHAR(64) COMMENT '绑定的工作流ID，用于工作流触发器功能';

-- 新增触发器调度频率字段
ALTER TABLE ops_task_attr_basic_replica
ADD COLUMN trigger_schedule_frequency INT COMMENT '触发器调度频率：0=30分钟、1=1小时、2=2小时';

-- 添加索引
CREATE INDEX idx_ops_task_attr_basic_replica_workflow_id ON ops_task_attr_basic_replica(workflow_id);
```

## 2. 实体类修改

### `OpsTaskAttrBasicReplica.java`
- 添加导入：`import sdata.ops.base.system.model.enums.TriggerScheduleFrequency;`
- 新增字段：
  ```java
  /**
   * 绑定的工作流ID
   */
  private String workflowId;

  /**
   * 触发器调度频率
   */
  private TriggerScheduleFrequency triggerScheduleFrequency;
  ```

## 3. Mapper 配置修改

### `OpsTaskAttrBasicReplicaMapper.xml`
- 在 `BaseResultMap` 中添加字段映射：
  ```xml
  <result property="workflowId" column="workflow_id" jdbcType="VARCHAR"/>
  <result property="triggerScheduleFrequency" column="trigger_schedule_frequency" jdbcType="INTEGER"/>
  ```
- 在 `Base_Column_List` 中添加字段：
  ```sql
  workflow_id,trigger_schedule_frequency
  ```

## 4. 服务层修改

### `WorkflowTriggerScheduleServiceImpl.java`

#### 4.1 依赖注入
- 添加 `OpsTaskAttrBasicReplicaService` 依赖
- 添加导入：`import java.time.LocalDateTime;`

#### 4.2 主要方法重构
- **`executeWorkflowTriggers()`**：同时查询主表和副本表的任务单元
- **`processTaskUnit()`**：处理主表任务单元的工作流执行
- **`processReplicaTaskUnit()`**：处理副本表任务单元的工作流执行
- **`createTaskFromReplicaWorkflowTrigger()`**：从副本表创建任务

#### 4.3 查询逻辑
```java
// 查询主表
LambdaQueryWrapper<OpsTaskAttrBasic> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.isNotNull(OpsTaskAttrBasic::getWorkflowId)
           .ne(OpsTaskAttrBasic::getWorkflowId, "")
           .isNotNull(OpsTaskAttrBasic::getTriggerScheduleFrequency)
           .eq(OpsTaskAttrBasic::getTaskStatus, 1);

// 查询副本表
LambdaQueryWrapper<OpsTaskAttrBasicReplica> replicaQueryWrapper = new LambdaQueryWrapper<>();
replicaQueryWrapper.isNotNull(OpsTaskAttrBasicReplica::getWorkflowId)
                  .ne(OpsTaskAttrBasicReplica::getWorkflowId, "")
                  .isNotNull(OpsTaskAttrBasicReplica::getTriggerScheduleFrequency)
                  .eq(OpsTaskAttrBasicReplica::getTaskStatus, 1);
```

#### 4.4 任务创建逻辑
- 主表：使用 `taskGenInfoService.convertGenInfo(OpsTaskAttrBasic)`
- 副本表：使用 `taskGenInfoService.convertGenInfo(OpsTaskAttrBasicReplica)`
- 两者都支持工作流返回的任务信息覆盖

## 5. 功能特性

### 5.1 统一的工作流执行
- 主表和副本表使用相同的工作流执行逻辑
- 传递 `taskUnitType` 参数区分来源（"basic" 或 "replica"）

### 5.2 任务创建
- 复用现有的 `convertGenInfo` 方法
- 支持工作流返回的任务信息覆盖默认配置
- 完整的错误处理和日志记录

### 5.3 调度频率支持
- 30分钟（枚举值：0）
- 1小时（枚举值：1）
- 2小时（枚举值：2）

## 6. 部署步骤

1. **执行数据库脚本**：
   ```bash
   mysql -u username -p database_name < sql/add-workflow-fields-to-replica.sql
   ```

2. **重启应用**：
   - 让新的实体类字段和 Mapper 配置生效
   - 让新的服务逻辑生效

3. **验证功能**：
   - 在副本表中配置 `workflow_id` 和 `trigger_schedule_frequency`
   - 观察调度日志确认副本表任务单元被正确处理

## 7. 日志输出示例

```
找到 2 个配置了工作流绑定且已上线的任务单元（主表）
找到 3 个配置了工作流绑定且已上线的任务单元（副本表）
执行副本任务单元 测试任务 的工作流触发器，工作流ID: workflow-123
工作流执行完成，触发结果: true, 副本任务单元: 测试任务
副本表工作流触发器创建任务成功，任务ID: 456789, 任务名称: 测试任务
工作流触发器主调度任务执行完成，共处理 5 个任务单元，执行 2 个，跳过 3 个
```

## 8. 注意事项

- 副本表的 ID 类型是 `String`，主表是 `Long`，已在代码中正确处理
- 副本表使用现有的 `convertGenInfo(OpsTaskAttrBasicReplica)` 方法创建任务
- 工作流执行时会传递 `taskUnitType` 参数，便于工作流内部区分处理逻辑
- 保持了与主表相同的错误处理和日志记录机制
