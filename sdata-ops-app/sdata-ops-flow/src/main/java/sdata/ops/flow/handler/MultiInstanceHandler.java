package sdata.ops.flow.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;
import sdata.ops.flowable.common.constant.ProcessConstants;
import sdata.ops.flowable.common.enums.UserTaskDataTypeEnum;
import sdata.ops.system.api.feign.SystemFeignService;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 多实例处理类
 *
 * <AUTHOR>
 * @since 2025-09-24
 */
@Slf4j
@AllArgsConstructor
@Component("multiInstanceHandler")
public class MultiInstanceHandler {
    private final SystemFeignService systemFeignService;

    public Set<String> getUserIds(DelegateExecution execution) {
        // 候选用户id
        Set<String> candidateUserIds = new LinkedHashSet<>();
        FlowElement flowElement = execution.getCurrentFlowElement();

        if (!ObjectUtil.isNotEmpty(flowElement) || !(flowElement instanceof UserTask)) {
            return candidateUserIds;
        }
        UserTask userTask = (UserTask) flowElement;
        String dataType = userTask.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, ProcessConstants.PROCESS_CUSTOM_DATA_TYPE);
        if (UserTaskDataTypeEnum.INITIATOR.is(dataType) && CollUtil.isNotEmpty(userTask.getCandidateUsers())) {
            // 发起人
            log.info("节点指派给发起人:{}", userTask.getAssignee());
            candidateUserIds.add(userTask.getAssignee());
        } else if (UserTaskDataTypeEnum.USER.is(dataType) && CollUtil.isNotEmpty(userTask.getCandidateUsers())) {
            // 指定用户
            log.info("节点指派给指定人员:{}", userTask.getCandidateUsers());
            candidateUserIds.addAll(userTask.getCandidateUsers());
        } else if (CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
            // 获取指定角色id、或者岗位id
            List<String> ids = userTask.getCandidateGroups().stream()
                    // 去掉前4位，前4位是前缀
                    .map(item -> item.substring(4))
                    .collect(Collectors.toList());

            if (UserTaskDataTypeEnum.ROLE.is(dataType)) {
                // 通过角色id，获取所有用户id集合
                log.info("节点指派给指定角色:{}", ids);
                Collection<String> userIds = systemFeignService.selectAllUserIdByRoleIds(ids);
                if (userIds == null || userIds.isEmpty()) {
                    log.warn("指定角色下无用户,角色id：{}", ids);
                } else {
                    candidateUserIds.addAll(userIds);
                }
            } else if (UserTaskDataTypeEnum.POSTS.is(dataType)) {
                // 通过岗位id，获取所有用户id集合
                log.info("节点指派给指定岗位:{}", ids);
                Collection<String> userIds = systemFeignService.selectAllUserIdsByPostIds(ids);
                if (userIds == null || userIds.isEmpty()) {
                    log.warn("指定岗位无用户");
                } else {
                    candidateUserIds.addAll(userIds);
                }
            }
        }
        return candidateUserIds;
    }
}
