-- 清理 trigger_schedule_frequency 字段的无效数据
-- 解决 "No enum constant sdata.ops.base.system.model.enums.TriggerScheduleFrequency.0" 错误
-- 简单方案：直接清理无效数据，避免影响其他功能
-- 创建时间: 2025-09-25

-- 查看当前数据分布
SELECT
    trigger_schedule_frequency,
    COUNT(*) as count
FROM ops_task_attr_basic
WHERE trigger_schedule_frequency IS NOT NULL
GROUP BY trigger_schedule_frequency;

-- 清理无效数据：将值为 0 的记录设置为 NULL
UPDATE ops_task_attr_basic
SET trigger_schedule_frequency = NULL
WHERE trigger_schedule_frequency = 0;

-- 验证清理结果
SELECT
    trigger_schedule_frequency,
    COUNT(*) as count
FROM ops_task_attr_basic
WHERE trigger_schedule_frequency IS NOT NULL
GROUP BY trigger_schedule_frequency;

-- 验证更新后的数据
-- SELECT 
--     trigger_schedule_frequency,
--     COUNT(*) as count
-- FROM ops_task_attr_basic 
-- WHERE trigger_schedule_frequency IS NOT NULL
-- GROUP BY trigger_schedule_frequency;
