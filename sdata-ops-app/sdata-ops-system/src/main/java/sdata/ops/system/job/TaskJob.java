package sdata.ops.system.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.base.system.model.vo.TaskScriptResultVO;
import sdata.ops.common.api.CommonConstant;
import sdata.ops.common.api.TaskConstant;
import sdata.ops.common.core.annotation.JobTaskDistributedLock;
import sdata.ops.common.core.util.TimeUtil;
import sdata.ops.system.mapper.OpsTaskTransferConfMapper;
import sdata.ops.system.service.*;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
// @EnableScheduling  // 已迁移到调度中心，禁用原@Scheduled任务
@Component
@Slf4j
public class TaskJob {

    /**
     * 每日生成定时任务(工作日)
     */
    private final static String CREATE_TASK_CRON = "0 0 1 * * ?";

    /**
     * 每日生成定时任务(工作日)特殊时间类型脚本，每日执行1次
     */
    private final static String CREATE_TASK_CRON_ONCES = "0 0 2 * * ?";
    /**
     * 定时更新任务启用状态
     */
    private final static String UPDATE_TASK_CRON = "0 0 6-22 * * ? ";

    private final OpsTaskGenInfoService opsTaskGenInfoService;
    private final OpsTaskAttrBasicService opsTaskAttrBasicService;
    private final OpsTaskTemplateService opsTaskTemplateService;
    private final OpsTaskAttrBasicReplicaService opsTaskAttrBasicReplicaService;
    private final OpsTradeTypeService opsTradeTypeService;
    private final OpsTaskTransferConfMapper opsTaskTransferConfMapper;

    /**
     * 每日生成任务
     */
//    @Scheduled(cron = CREATE_TASK_CRON)
    @JobTaskDistributedLock(lockId = "1001")
    public void creatTask() {
        // 1、判断当日是否工作日
        if (!opsTradeTypeService.checkWorkdayByToday()) {
            log.info("gen-every:每日任务清单生成终止,当天非工作日");
            return;
        }
        opsTaskGenInfoService.realDeleted(DateUtil.format(new Date(), "yyyy-MM-dd"));
        log.info("gen-every:每日任务清单生成开始");
        // 2、根据上线单元生成任务
        LambdaQueryWrapper<OpsTaskAttrBasic> basicWrapper = Wrappers.lambdaQuery();
        //任务状态是上线且，非明细的
        basicWrapper.eq(OpsTaskAttrBasic::getTaskStatus, 1);
        basicWrapper.ne(OpsTaskAttrBasic::getImportStatus, 1);
        basicWrapper.eq(OpsTaskAttrBasic::getTaskCreateType, 0);
        List<OpsTaskAttrBasic> basicList = opsTaskAttrBasicService.list(basicWrapper);
        List<OpsTaskGenInfo> genInfoList = basicList.stream().map(opsTaskGenInfoService::convertGenInfo).collect(Collectors.toList());
        log.info("gen-every:每日任务清单生成::根据任务单元生成数据{}", genInfoList.size());

        // 3、根据模板生成任务
        // 3.1 查询上线的模板
        List<OpsTaskTemplate> templateList = opsTaskTemplateService.list(Wrappers.lambdaQuery(OpsTaskTemplate.class).eq(OpsTaskTemplate::getTemplateStatus, 1));
        List<String> templateIdList = templateList.stream().map(OpsTaskTemplate::getId).collect(Collectors.toList());

        if (!templateIdList.isEmpty()) {
            //查询模板下所有配置，并替换所有id与父id,且子级节点也一并更新
            for (String templateId : templateIdList) {
                List<OpsTaskAttrBasicReplica> basicUnits = opsTaskAttrBasicReplicaService.queryListByTemplateId(templateId);
                //过滤出模板中非明细任务的单元信息
                List<OpsTaskGenInfo> saveBatch = basicUnits.stream().filter(e -> e.getImportStatus().equals(0)).map(opsTaskGenInfoService::convertGenInfo).collect(Collectors.toList());
                opsTaskGenInfoService.replaceIdAndFillChildIdsAndSort(saveBatch);
                genInfoList.addAll(saveBatch);
            }
            log.info("gen-every:每日任务清单生成::根据任务模板绑定单元生成数据{}", genInfoList.size());
        }

        // 4、判断是否进行隐藏、展示
        int count = 0;
        for (OpsTaskGenInfo info : genInfoList) {
            info.setDeleted(CommonConstant.DEL_FLAG_1.toString());
            // 4.1 触发类型为日常任务默认可见，其他类型则需要通过轮询触发器按照脚本指标认定是否可见
            if (Objects.equals(info.getTaskTriggerType(), TaskConstant.TASK_TYPE_DAILY)) {
                info.setDeleted(CommonConstant.DEL_FLAG_0.toString());
                count++;
            }

        }
        log.info("gen-every:每日任务清单生成:: 日常直接展示任务数量 {}", count);

        // 5、数据入库
        if (!genInfoList.isEmpty()) {
            //自动转派配置
            autoTransfer(genInfoList);
            opsTaskGenInfoService.saveBatch(genInfoList, 99);
        }

        log.info("gen-every:每日任务清单生成::结束");

    }


    /***
     *  自动转派
     */
    void autoTransfer(List<OpsTaskGenInfo> genInfoList) {
        log.info("查询是否有未来生效的转派配置");
        Date today = DateUtil.beginOfDay(new Date());
        LambdaQueryWrapper<OpsTaskTransferConf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(
                OpsTaskTransferConf::getTaskUnitId,
                genInfoList.stream().map(OpsTaskGenInfo::getTaskRefId).collect(Collectors.toList())
        );
        queryWrapper.and(wrapper ->
                wrapper.le(OpsTaskTransferConf::getTrStartTime, DateUtil.endOfDay(today))
                        .or()
                        .isNull(OpsTaskTransferConf::getTrStartTime)
        );
        queryWrapper.ge(OpsTaskTransferConf::getTrEndTime, today);
        queryWrapper.orderByDesc(OpsTaskTransferConf::getTrEndTime);
        List<OpsTaskTransferConf> opsTaskTransferConfList = opsTaskTransferConfMapper.selectList(queryWrapper);
        /**
         *  add 内容更新为原始时间 由于数据库存储精度到时分秒，所以内存重置
         */
        opsTaskTransferConfList.forEach(i -> {
            i.setTrEndTime(DateUtil.endOfDay(i.getTrEndTime()));
            i.setTrStartTime(i.getTrStartTime() == null ? null : DateUtil.beginOfDay(i.getTrStartTime()));
        });
        opsTaskTransferConfList = new ArrayList<>(opsTaskTransferConfList.stream().collect(Collectors.toMap(
                OpsTaskTransferConf::getTaskUnitId,
                task -> task,
                (existing, replacement) -> existing.getCreateTime().after(replacement.getCreateTime()) ? existing : replacement
        )).values());
        if (opsTaskTransferConfList.isEmpty()) {
            log.info("未发现有未来生效的转派配置,结束...");
            return;
        }
        //add 2023-03-19 计算哪些任务需要讲pid置为0
        List<String> setIds = groupBySetIds(opsTaskTransferConfList);
        List<OpsTaskGenInfo> trans = new ArrayList<>();
        for (OpsTaskGenInfo genInfo : genInfoList) {
            Optional<OpsTaskTransferConf> matchingConf = opsTaskTransferConfList.stream()
                    .filter(e ->
                            e.getTaskUnitId().equals(Long.valueOf(genInfo.getTaskRefId())) &&
                                    DateUtil.beginOfDay(e.getTrEndTime()).isAfterOrEquals(today) &&
                                    (e.getTrStartTime() == null || DateUtil.beginOfDay(e.getTrStartTime()).isBeforeOrEquals(today))
                    )
                    .findFirst();
            if (matchingConf.isPresent()) {
                trans.add(genInfo);
                if (setIds.contains(genInfo.getTaskRefId())) {
                    genInfo.setParentId(0L);
                }
                OpsTaskTransferConf opsTaskTransferConf = matchingConf.get();
                Map<String, String> configMap = JSONUtil.toBean(opsTaskTransferConf.getTrContent(), Map.class);
                if (configMap.get("type").equals("1")) {
                    if (configMap.get("transferType").equals("1")) {
                        genInfo.setTaskOwnerType("1");
                        genInfo.setTaskOwnerId(configMap.get("orgId"));
                        genInfo.setTaskOwnerVal(configMap.get("orgName"));
                        genInfo.setTaskTransferDesc(configMap.get("taskDesc"));
                        genInfo.setTaskTransferStatus(1);
                        genInfo.setOwnerOrgId(configMap.get("orgId"));
                    } else {
                        genInfo.setTaskCheckTransferStatus(1);
                        genInfo.setTaskCheckType("1");
                        genInfo.setTaskCheckId(configMap.get("orgId"));
                        genInfo.setTaskCheckVal(configMap.get("orgName"));
                    }
                    genInfo.setTaskTransferUserId(configMap.get("currentUserId"));
                }
                if (configMap.get("type").equals("2")) {
                    if (configMap.get("transferType").equals("1")) {
                        genInfo.setTaskOwnerType("2");
                        genInfo.setTaskOwnerId(configMap.get("userId"));
                        genInfo.setTaskOwnerVal(configMap.get("userName"));
                        genInfo.setTaskTransferDesc(configMap.get("taskDesc"));
                        genInfo.setTaskTransferStatus(1);
                        genInfo.setOwnerOrgId(configMap.get("orgId"));
                    } else {
                        genInfo.setTaskCheckTransferStatus(1);
                        genInfo.setTaskCheckType("2");
                        genInfo.setTaskCheckId(configMap.get("userId"));
                        genInfo.setTaskCheckVal(configMap.get("userName"));
                    }

                    genInfo.setTaskTransferUserId(configMap.get("currentUserId"));

                }
            }
        }
        updateParentTaskProperties(trans, genInfoList);
    }

    public void updateParentTaskProperties(List<OpsTaskGenInfo> children, List<OpsTaskGenInfo> all) {
        //获取转派的所有任务id
        List<String> tranIds = children.stream().map(i -> i.getId() + "").collect(Collectors.toList());
        for (OpsTaskGenInfo item : all) {
            //查询是否包含此id 的父任务且不在今天转派任务内的任务
            if (item.getTaskChildIds() != null && !tranIds.contains(item.getId() + "")) {
                //剔除掉当前转派的任务id
                List<String> ids = new ArrayList<>(List.of(item.getTaskChildIds().split(",")));
                if (ids.removeAll(tranIds)) {
                    //生成新的子任务ids集合
                    item.setTaskChildIds(String.join(",", ids));
                }
            }
        }
    }
    /**
     * 查询被转派的任务，对任务进行分组，分组后在每个分组中查询是否有pid找不到pid的进入list
     *
     * @param opsTaskTransferConfList 转派配置
     * @return arr
     */
    private List<String> groupBySetIds(List<OpsTaskTransferConf> opsTaskTransferConfList) {
        List<String> ids = new ArrayList<>();
        Map<String, List<JSONObject>> arr = opsTaskTransferConfList.stream().map(i -> JSONUtil.toBean(i.getTrContent(), JSONObject.class).set("taskId", i.getTaskUnitId())).collect(Collectors.groupingBy(
                i -> i.getStr("orgId")
        ));
        List<String> currTaskIds = opsTaskTransferConfList.stream().map(i -> i.getTaskUnitId() + "").collect(Collectors.toList());
        List<OpsTaskAttrBasicReplica> idMapping = opsTaskAttrBasicReplicaService.list(Wrappers.lambdaQuery(OpsTaskAttrBasicReplica.class).
                select(OpsTaskAttrBasicReplica::getId, OpsTaskAttrBasicReplica::getParentId).
                in(OpsTaskAttrBasicReplica::getId, currTaskIds));
        //获取当前任务的父级id
        Map<String, String> idMap = idMapping.stream().collect(Collectors.toMap(OpsTaskAttrBasicReplica::getId, OpsTaskAttrBasicReplica::getParentId));
        arr.forEach((k, v) -> {
            v.forEach(i -> {
                if (v.stream().noneMatch(e -> e.getStr("taskId").equals(idMap.get(i.getStr("taskId"))))) {
                    ids.add(i.getStr("taskId"));
                }
            });
        });
        return ids;
    }

    /**
     * 每日定时更新三方系统数据抓取 比如 oa与邮件的
     * 取数脚本配置，规则管理中数据同步类型的规则。在当前方法中，一小时执行一次
     */
//    @Scheduled(cron = UPDATE_TASK_CRON)
    public void obtThirdSystemDataForSch() {
        if (!opsTradeTypeService.checkWorkdayByToday()) {
            log.info("every-query-data:每日执行三方系统更新抓取数据逻辑:: 结束 :: 非工作日");
            return;
        }
        log.info("every-query-data:每日执行三方系统更新抓取数据逻辑:: 开始");
        List<OpsTradeType> opsTradeTypes = opsTradeTypeService.list(Wrappers.lambdaQuery(OpsTradeType.class).eq(OpsTradeType::getTypeVal, 2));
        if (opsTradeTypes.isEmpty()) {
            log.info("every-query-data:每日执行三方系统更新抓取数据逻辑:: 结束  ::  数据同步规则指标为空 ");
            return;
        }
        List<String> ids = opsTradeTypes.stream().map(OpsTradeType::getId).collect(Collectors.toList());
        opsTradeTypeService.batchCheckDateType(ids);
        log.info("every-query-data:每日执行三方系统更新抓取数据逻辑:: 结束  ::  数据同步规则触发:: {} ", ids);
    }

    /**
     * 1.任务自定义脚本轮询执行更新任务可见性。
     */
//    @Scheduled(cron = UPDATE_TASK_CRON)
    public void updateTaskIsSearchForOaSystemOrMailServer() {
        if (!opsTradeTypeService.checkWorkdayByToday()) {
            log.info("every-check:每日定时检查任务清单中触发器类型检查是否满足条件::结束::当天非工作日");
            return;
        }
        log.info("every-check:每日定时检查任务清单中触发器类型检查是否满足条件::开始");
        String today = DateUtil.today();
        // 1、查询当日不可见的任务
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.gt(OpsTaskGenInfo::getCreateTime, today);
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, CommonConstant.DEL_FLAG_1);
        queryWrapper.isNotNull(OpsTaskGenInfo::getTaskTriggerId);
        List<OpsTaskGenInfo> genInfoList = opsTaskGenInfoService.list(queryWrapper);
        if (genInfoList.isEmpty()) {
            log.info("every-check:每日定时检查任务清单中触发器类型检查是否满足条件::结束::触发器绑定任务为 0");
            return;
        }
        // 2、根据脚本判断是否可见
        List<String> triggerIdList = genInfoList.stream().map(OpsTaskGenInfo::getTaskTriggerId).distinct().collect(Collectors.toList());
        List<OpsTradeType> opsTradeTypes = opsTradeTypeService.listByIds(triggerIdList);
        //3.取规则管理为1- 工作日轮询的脚本
        List<String> eachIds = opsTradeTypes.stream().filter(i -> i.getTypeVal() == 1).map(OpsTradeType::getId).collect(Collectors.toList());
        log.info("every_check: 每日轮询执行脚本数量 {}", eachIds.size());
        Map<String, TaskScriptResultVO> resultMap = opsTradeTypeService.batchCheckDateType(eachIds);
        for (OpsTaskGenInfo opsTaskGenInfo : genInfoList) {
            String taskTriggerId = opsTaskGenInfo.getTaskTriggerId();
            if (resultMap.containsKey(taskTriggerId)) {
                log.info("非明细任务命中:: {}", opsTaskGenInfo.getTaskName());
                TaskScriptResultVO resultVO = resultMap.get(taskTriggerId);
                if (!resultVO.getStatus()) {
                    continue;
                }
                //分两步 update类型的
                if (opsTaskGenInfo.getTaskCreateType() == 0) {
                    LambdaUpdateWrapper<OpsTaskGenInfo> updateWrapper = Wrappers.lambdaUpdate();
                    updateWrapper.set(OpsTaskGenInfo::getDeleted, CommonConstant.DEL_FLAG_0);
                    updateWrapper.set(StrUtil.isNotEmpty(resultVO.getStartTime()), OpsTaskGenInfo::getTaskStartTime, format(resultVO.getStartTime()));
                    updateWrapper.set(StrUtil.isNotEmpty(resultVO.getEndTime()), OpsTaskGenInfo::getTaskEndTime, format(resultVO.getEndTime()));
                    //工作量计数
                    updateWrapper.set(resultVO.getCount() != null, OpsTaskGenInfo::getWorkAmount, resultVO.getCount());
                    updateWrapper.eq(OpsTaskGenInfo::getId, opsTaskGenInfo.getId());
                    opsTaskGenInfoService.update(updateWrapper);
                }
            }
        }

        log.info("every-check:每日定时检查任务清单中触发器类型检查是否满足条件::结束");
    }


    public void  insertProxy(){
        insertTaskForOaSystemOrMailServer();
    }
    /**
     * 1.任务增量创建来源于 oa或者邮件命中
     */
//    @Scheduled(cron = UPDATE_TASK_CRON)
    @JobTaskDistributedLock(lockId = "1002")
    public void insertTaskForOaSystemOrMailServer() {
        if (!opsTradeTypeService.checkWorkdayByToday()) {
            log.info("every_check_insert:每日轮询查询oa与邮箱是否命中，增量创建任务清单::结束::当天非工作日");
            return;
        }
        List<OpsTaskAttrBasicReplica> basicUnits = opsTaskAttrBasicReplicaService.queryListByTemplateIdOfCreateType1();
        if (!basicUnits.isEmpty()) {

            List<String> triggerIdList = basicUnits.stream().map(OpsTaskAttrBasicReplica::getTaskTriggerId).distinct().collect(Collectors.toList());
            //类型为 1 的
            List<OpsTradeType> opsTradeTypes = opsTradeTypeService.list(Wrappers.lambdaQuery(OpsTradeType.class).in(OpsTradeType::getId, triggerIdList).eq(OpsTradeType::getTypeVal, 1));
            log.info("every_check_insert: 每日轮询执行脚本数量 {}", opsTradeTypes.size());
            if(!opsTradeTypes.isEmpty()) {
                //执行脚本结果
                Map<String, TaskScriptResultVO> arrInsert = opsTradeTypeService.batchCheckDateType(opsTradeTypes.stream().map(OpsTradeType::getId).collect(Collectors.toList()));
                List<OpsTaskGenInfo> saveBatch = basicUnits.stream().filter(e -> e.getImportStatus().equals(0)).map(opsTaskGenInfoService::convertGenInfo).collect(Collectors.toList());
                opsTaskGenInfoService.replaceIdAndFillChildIdsAndSort(saveBatch);
                //过滤出模板中非明细任务的单元信息
                for (OpsTaskGenInfo info : saveBatch) {
                    if (arrInsert.containsKey(info.getTaskRefId())) {
                        TaskScriptResultVO resultVO = arrInsert.get(info.getTaskRefId());
                        if (resultVO.getCount() != null) {
                            info.setWorkAmount(resultVO.getCount());
                        }
                        if (resultVO.getEndTime() != null && TimeUtil.validate_yyyy_MM_dd(resultVO.getEndTime())) {
                            info.setTaskEndTime(DateUtil.parse(resultVO.getEndTime(), "yyyy-MM-dd HH:mm"));
                        }
                        if (resultVO.getStartTime() != null && TimeUtil.validate_yyyy_MM_dd(resultVO.getStartTime())) {
                            info.setTaskStartTime(DateUtil.parse(resultVO.getStartTime(), "yyyy-MM-dd HH:mm"));
                        }
                    }
                }
                opsTaskGenInfoService.saveBatch(saveBatch);
            }
        }
        log.info("every_check_insert: 执行结束:::");
    }

    @Value("${third.sw.end:null}")
    private String swImport;

    /**
     * 特殊明细 任务更新抓取数据，写入明细表逻辑
     */
//    @Scheduled(cron = UPDATE_TASK_CRON)
    public void updateSpecDetailTask() {

        // 3.1 查询的模板
        List<OpsTaskTemplate> templateList;
        if (!StringUtils.hasText(swImport)) {
            templateList = opsTaskTemplateService.list(Wrappers.lambdaQuery(OpsTaskTemplate.class).eq(OpsTaskTemplate::getTemplateStatus, 1));
        } else {
            templateList = opsTaskTemplateService.list();
        }

        List<String> templateIdList = templateList.stream().map(OpsTaskTemplate::getId).collect(Collectors.toList());

        if (!templateIdList.isEmpty()) {
            //查询模板下所有配置，并替换所有id与父id,且子级节点也一并更新
            List<OpsTaskAttrBasicReplica> basicUnits = opsTaskAttrBasicReplicaService.queryReplicaHaveImportListByTemplateId(templateIdList);
            Map<String, TaskScriptResultVO> resultMap = opsTradeTypeService.batchCheckDateType(basicUnits.stream().filter(i -> StringUtils.hasText(i.getTaskTriggerId())).map(OpsTaskAttrBasicReplica::getTaskTriggerId).collect(Collectors.toList()));
            resultMap.forEach((k, v) -> {
                log.error("明细任务一小时执行一次解析任务id{}  执行结果 {}", k, v.getStatus());
            });
        }
    }


    /**
     * 定时更新任务启用状态(每日执行执行，脚本为特殊时间周期的类型的)
     */
//    @Scheduled(cron = CREATE_TASK_CRON_ONCES)
    public void updateTaskOnlyOnce() {
        if (!opsTradeTypeService.checkWorkdayByToday()) {
            log.info("every-check:每日定时检查任务清单中触发器类型检查是否满足条件::结束::当天非工作日");
            return;
        }
        log.info("every-check:每日定时一次执行(时间类型脚本) 检查任务清单中触发器类型检查是否满足条件::开始");
        String today = DateUtil.today();
        // 1、查询当日不可见的任务
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.gt(OpsTaskGenInfo::getCreateTime, today);
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, CommonConstant.DEL_FLAG_1);
        queryWrapper.isNotNull(OpsTaskGenInfo::getTaskTriggerId);
        List<OpsTaskGenInfo> genInfoList = opsTaskGenInfoService.list(queryWrapper);
        if (genInfoList.isEmpty()) {
            log.info("every-check:每日定时一次执行(时间类型脚本) 检查任务清单中触发器类型检查是否满足条件::结束::触发器绑定任务为 0");
            return;
        }
        // 2、根据脚本判断是否可见
        List<String> triggerIdList = genInfoList.stream().map(OpsTaskGenInfo::getTaskTriggerId).distinct().collect(Collectors.toList());
        List<OpsTradeType> opsTradeTypes = opsTradeTypeService.listByIds(triggerIdList);
        //3.时间类型脚本，跨度为日的，每日执行一次
        List<String> onlyOncesIds = opsTradeTypes.stream().filter(i -> i.getTypeVal() == 0).map(OpsTradeType::getId).collect(Collectors.toList());
        log.info("every_check: 每日固定执行一次脚本数量 {}", onlyOncesIds.size());
        Map<String, TaskScriptResultVO> resultMap = opsTradeTypeService.batchCheckDateType(onlyOncesIds);
        for (OpsTaskGenInfo opsTaskGenInfo : genInfoList) {
            String taskTriggerId = opsTaskGenInfo.getTaskTriggerId();
            if (resultMap.containsKey(taskTriggerId)) {
                TaskScriptResultVO resultVO = resultMap.get(taskTriggerId);
                if (!resultVO.getStatus()) {
                    continue;
                }
                log.info("every_check: 每日固定执行一次脚本成功id{}", taskTriggerId);
                LambdaUpdateWrapper<OpsTaskGenInfo> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(OpsTaskGenInfo::getDeleted, CommonConstant.DEL_FLAG_0);
                updateWrapper.set(StrUtil.isNotEmpty(resultVO.getStartTime()), OpsTaskGenInfo::getTaskStartTime, format(resultVO.getStartTime()));
                updateWrapper.set(StrUtil.isNotEmpty(resultVO.getEndTime()), OpsTaskGenInfo::getTaskEndTime, format(resultVO.getEndTime()));
                updateWrapper.eq(OpsTaskGenInfo::getId, opsTaskGenInfo.getId());
                opsTaskGenInfoService.update(updateWrapper);
            }
        }
        log.info("every-check:每日定时一次执行(时间类型脚本) 检查任务清单中触发器类型检查是否满足条件::结束");
    }


    private Date format(String dateStr) {
        return DateUtil.parse(dateStr, "yyyy-MM-dd HH:mm");
    }
}
