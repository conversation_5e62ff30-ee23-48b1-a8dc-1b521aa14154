package sdata.ops.system.handler;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import sdata.ops.base.system.model.dto.OpsTaskInfoUpdateDTO;
import sdata.ops.base.system.model.entity.*;
import sdata.ops.base.system.model.vo.SystemTestVO;
import sdata.ops.base.system.model.vo.WorkDayVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.config.feign.FeignLocal;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.system.api.feign.SystemFeignService;
import sdata.ops.system.service.*;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@FeignLocal(SystemFeignService.class)
@RequiredArgsConstructor
public class SystemFeignLocalHandler implements SystemFeignService {

    private final SystemUserService systemUserService;

    private final OpsTaskFundInfoService fundInfoService;

    private final OpsTradeTypeService tradeTypeService;

    private final OpsSysDataPermService opsSysDataPermService;

    private final OpsSysDictItemService sysDictItemService;

    private final OpsTaskGenInfoService opsTaskGenInfoService;

    // 注入TaskJob用于调度中心触发任务
    private final sdata.ops.system.job.TaskJob taskJob;

    @Override
    public SystemTestVO getTestVo() {
        return null;
    }

    @Override
    public SystemTestVO getTransVo() {
        return null;
    }

    @Override
    public Map<String, String> idNameMapper() {
        return systemUserService.findNameIdMapping();
    }

    @Override
    public R<String> saveFundInfo(OpsTaskInfoUpdateDTO dto) {
        fundInfoService.saveFromIndicator(dto, "system");
        return R.success("完成");
    }

    @Override
    public List<OpsTaskFundInfo> queryFundInfos(String replicaId) {
        return fundInfoService.list(Wrappers.lambdaQuery(OpsTaskFundInfo.class)
                .eq(OpsTaskFundInfo::getTaskReplicaId, replicaId).
                eq(OpsTaskFundInfo::getBizDate, DateUtil.format(new Date(), "yyyyMMdd")));
    }

    @Override
    public R<Boolean> checkTodayIsWorkDay(String date) {
        return R.data(tradeTypeService.checkWorkdayByToday());
    }

    @Override
    public R<String> getLastWorkDay(String date) {
        return R.data(tradeTypeService.getLastWorkDay(date));
    }

    @Override
    public R<Boolean> deleteTaImportDate(String date) {
        LambdaQueryWrapper<OpsTaskFundInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskFundInfo::getDataId, -200);
        queryWrapper.eq(OpsTaskFundInfo::getBizDate, date);
        fundInfoService.remove(queryWrapper);
        return R.success("");
    }

    @Override
    public R<Boolean> deleteFundInfoImportByDateAndDataId(String date, String dataId) {
        LambdaQueryWrapper<OpsTaskFundInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpsTaskFundInfo::getDataId, dataId);
        queryWrapper.eq(OpsTaskFundInfo::getBizDate, date);
        fundInfoService.remove(queryWrapper);
        return R.success("");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> saveAllFund(List<OpsTaskFundInfo> arr) {
        fundInfoService.saveBatch(arr);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @Override
    public R<String> getNextWorkDay(String today) {
        return R.data(tradeTypeService.getNextWorkDay(today));
    }

    @Override
    public String getWorkDay(WorkDayVO vo) {
        if (vo.getOffset() > 0) {
            return tradeTypeService.getNextWorkDayOffset(vo.getDay(), vo.getOffset());
        }
        return tradeTypeService.getLastWorkDayOffset(vo.getDay(), Math.abs(vo.getOffset()));
    }

    @Override
    public R<Object> getById(String id) {
        return R.data(opsSysDataPermService.getById(id));
    }

    @Override
    public JSONObject getDataPermById(String id, String userId) {
        return opsSysDataPermService.getDataPermById(id, userId);
    }

    @Override
    public R<List<OpsSysDictItem>> dictListByTypes(List<String> dictType) {
        List<OpsSysDictItem> list = sysDictItemService.lambdaQuery()
                .in(CollUtil.isNotEmpty(dictType), OpsSysDictItem::getDictType, dictType)
                .list();
        return R.data(list);
    }

    @Override
    public String selectOrgNameById(Long deptId) {
        if (deptId == null) {
            return "";
        }

        OpsSysOrg org = SpringBeanUtil.getBean(OpsSysOrgService.class).getById(deptId);
        if (org == null) {
            return "";
        }
        return org.getOrgName();
    }

    @Override
    public String selectRoleNameById(Long roleId) {
        if (roleId == null) {
            return "";
        }
        OpsSysRole role = SpringBeanUtil.getBean(OpsSysRoleService.class).getById(roleId);
        if (role == null) {
            return "";
        }
        return role.getRoleName();
    }

    @Override
    public String selectNickNameById(Long userId) {
        if (userId == null) {
            return "";
        }
        SystemUser user = systemUserService.getById(userId);
        return user.getName();
    }

    @Override
    public List<String> findSameDeptUserIds(String userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        List<Long> orgIds = SpringBeanUtil.getBean(OpsSysUserOrgService.class).query()
                .select("DISTINCT org_id")
                .eq("user_id", userId)
                .list()
                .stream()
                .map(OpsSysUserOrg::getOrgId)
                .collect(Collectors.toList());
        if (orgIds.isEmpty()) {
            return Collections.emptyList();
        }
        // TODO 还需要补充下级部门的用户id
        return systemUserService.lambdaQuery()
                .select(BaseEntity::getId)
                .in(SystemUser::getDept, orgIds)
                .list()
                .stream()
                .map(BaseEntity::getId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemUser> findAllByPerUser(String deptId) {
        // TODO
        return List.of();
    }

    @Override
    public List<String> selectMyRoleIds(String userId) {
        return SpringBeanUtil.getBean(OpsSysUserRoleService.class).query()
                .select("DISTINCT role_id")
                .eq("user_id", userId)
                .list()
                .stream()
                .map(OpsSysUserRole::getRoleId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> selectMyOrgIds(String userId) {
        return SpringBeanUtil.getBean(OpsSysUserOrgService.class).query()
                .select("DISTINCT org_id")
                .eq("user_id", userId)
                .list()
                .stream()
                .map(OpsSysUserOrg::getOrgId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public List<OpsSysOrg> selectChildrenDeptById(String dept) {
        // TODO
        return List.of();
    }

    @Override
    public Boolean isTradeDay(LocalDate date, String market) {
        return SpringBeanUtil.getBean(OpsSysCalendarService.class).isTradeDay(date, market);
    }

    @Override
    public List<String> selectAllUserIdByRoleIds(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return SpringBeanUtil.getBean(OpsSysUserRoleService.class)
                .query()
                .select("DISTINCT user_id")
                .in("role_id", ids)
                .list()
                .stream()
                .map(OpsSysUserRole::getUserId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> selectAllUserIdsByPostIds(List<String> ids) {
        return SpringBeanUtil.getBean(OpsSysUserOrgService.class)
                .query()
                .select("DISTINCT user_id")
                .in("org_id", ids)
                .list()
                .stream()
                .map(OpsSysUserOrg::getUserId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    // ========== 任务中心@Scheduled任务迁移到调度中心的触发方法实现 ==========

    @Override
    public R<String> triggerCreatTask() {
        try {
            taskJob.creatTask();
            return R.success("每日任务清单生成执行成功");
        } catch (Exception e) {
            return R.fail("每日任务清单生成执行失败: " + e.getMessage());
        }
    }

    @Override
    public R<String> triggerObtThirdSystemDataForSch() {
        try {
            taskJob.obtThirdSystemDataForSch();
            return R.success("三方系统数据抓取执行成功");
        } catch (Exception e) {
            return R.fail("三方系统数据抓取执行失败: " + e.getMessage());
        }
    }

    @Override
    public R<String> triggerUpdateTaskIsSearchForOaSystemOrMailServer() {
        try {
            taskJob.updateTaskIsSearchForOaSystemOrMailServer();
            return R.success("任务可见性轮询更新执行成功");
        } catch (Exception e) {
            return R.fail("任务可见性轮询更新执行失败: " + e.getMessage());
        }
    }

    @Override
    public R<String> triggerInsertTaskForOaSystemOrMailServer() {
        try {
            taskJob.insertTaskForOaSystemOrMailServer();
            return R.success("增量任务创建执行成功");
        } catch (Exception e) {
            return R.fail("增量任务创建执行失败: " + e.getMessage());
        }
    }

    @Override
    public R<String> triggerUpdateSpecDetailTask() {
        try {
            taskJob.updateSpecDetailTask();
            return R.success("特殊明细任务更新执行成功");
        } catch (Exception e) {
            return R.fail("特殊明细任务更新执行失败: " + e.getMessage());
        }
    }

    @Override
    public R<String> triggerUpdateTaskOnlyOnce() {
        try {
            taskJob.updateTaskOnlyOnce();
            return R.success("特殊时间类型脚本执行成功");
        } catch (Exception e) {
            return R.fail("特殊时间类型脚本执行失败: " + e.getMessage());
        }
    }

    @Override
    public R<String> triggerTimerEverDayScannerDelayTaskStatus() {
        try {
            opsTaskGenInfoService.timerEverDayScannerDelayTaskStatus();
            return R.success("延期任务状态扫描执行成功");
        } catch (Exception e) {
            return R.fail("延期任务状态扫描执行失败: " + e.getMessage());
        }
    }

    @Override
    public R<String> triggerExecuteWorkflowTriggers() {
        try {
            WorkflowTriggerScheduleService workflowTriggerService = SpringBeanUtil.getBean(WorkflowTriggerScheduleService.class);
            String result = workflowTriggerService.executeWorkflowTriggers();
            return R.success(result);
        } catch (Exception e) {
            return R.fail("工作流触发器主调度任务执行失败: " + e.getMessage());
        }
    }
}
