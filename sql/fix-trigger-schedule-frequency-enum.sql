-- 修复 TriggerScheduleFrequency 枚举映射问题
-- 解决 "No enum constant sdata.ops.base.system.model.enums.TriggerScheduleFrequency.0" 错误
-- 创建时间: 2025-09-25

-- 查看当前数据分布
SELECT 
    trigger_schedule_frequency,
    COUNT(*) as count
FROM ops_task_attr_basic 
WHERE trigger_schedule_frequency IS NOT NULL
GROUP BY trigger_schedule_frequency;

-- 可选：如果需要将现有的无效数据设置为有效值
-- 注意：执行前请确认业务需求

-- 将值为 0 的记录更新为 NULL（表示未设置）
-- UPDATE ops_task_attr_basic 
-- SET trigger_schedule_frequency = NULL 
-- WHERE trigger_schedule_frequency = 0;

-- 或者将值为 0 的记录更新为默认的 30 分钟频率
-- UPDATE ops_task_attr_basic 
-- SET trigger_schedule_frequency = 1 
-- WHERE trigger_schedule_frequency = 0;

-- 验证更新后的数据
-- SELECT 
--     trigger_schedule_frequency,
--     COUNT(*) as count
-- FROM ops_task_attr_basic 
-- WHERE trigger_schedule_frequency IS NOT NULL
-- GROUP BY trigger_schedule_frequency;
