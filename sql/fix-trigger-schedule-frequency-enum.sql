-- 修复 TriggerScheduleFrequency 枚举映射问题
-- 解决 "No enum constant sdata.ops.base.system.model.enums.TriggerScheduleFrequency.0" 错误
-- 问题原因：数据库字段是 VARCHAR(10)，应该改为 INT 类型
-- 解决方案：修改字段类型为 INT，使用枚举序号映射
-- 创建时间: 2025-09-25

-- 查看当前数据分布
SELECT
    trigger_schedule_frequency,
    COUNT(*) as count
FROM ops_task_attr_basic
WHERE trigger_schedule_frequency IS NOT NULL
GROUP BY trigger_schedule_frequency;

-- 1. 修改字段类型为 INT
ALTER TABLE ops_task_attr_basic
MODIFY COLUMN trigger_schedule_frequency INT COMMENT '触发器调度频率：0=30分钟、1=1小时、2=2小时';

-- 2. 枚举映射关系（使用序号）：
-- 0 -> THIRTY_MINUTES (30分钟)
-- 1 -> ONE_HOUR (1小时)
-- 2 -> TWO_HOURS (2小时)

-- 3. 如果之前有字符串数据，需要转换（根据实际情况调整）
-- UPDATE ops_task_attr_basic SET trigger_schedule_frequency = 0 WHERE trigger_schedule_frequency = '30m';
-- UPDATE ops_task_attr_basic SET trigger_schedule_frequency = 1 WHERE trigger_schedule_frequency = '1h';
-- UPDATE ops_task_attr_basic SET trigger_schedule_frequency = 2 WHERE trigger_schedule_frequency = '2h';
-- UPDATE ops_task_attr_basic SET trigger_schedule_frequency = NULL WHERE trigger_schedule_frequency = '0';

-- 验证更新后的数据
-- SELECT 
--     trigger_schedule_frequency,
--     COUNT(*) as count
-- FROM ops_task_attr_basic 
-- WHERE trigger_schedule_frequency IS NOT NULL
-- GROUP BY trigger_schedule_frequency;
