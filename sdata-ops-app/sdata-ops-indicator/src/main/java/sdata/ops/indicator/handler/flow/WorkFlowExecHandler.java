package sdata.ops.indicator.handler.flow;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONObject;
import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.ChainNode;
import com.agentsflex.core.chain.ChainStatus;
import com.agentsflex.core.chain.event.ChainStatusChangeEvent;
import com.agentsflex.core.chain.event.NodeEndEvent;
import dev.tinyflow.core.Tinyflow;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import sdata.ops.base.flow.model.entity.OpsAiWorkflow;
import sdata.ops.base.indicator.model.vo.WorkflowTriggerResultVO;
import sdata.ops.common.core.util.SpringBeanUtil;
import sdata.ops.indicator.service.OpsWorkflowLogSummaryService;
import sdata.ops.flow.api.feign.WorkFlowFeignService;
import sdata.ops.indicator.handler.flow.config.FlowPreparationConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流执行器
 * desc: read flow from db-service then execute
 * author: zw
 * date: 2025/06/18
 */
@Component
@RequiredArgsConstructor
public class WorkFlowExecHandler {


    private final FlowPreparationConfig flowPreparationConfig;

    private final WorkFlowFeignService workFlowFeignService;

    private final OpsWorkflowLogSummaryService logSummaryService;

    public void initProvider(Tinyflow tinyflow) {
        //初始化相关提供者
        flowPreparationConfig.initProvidersAndNodeParsers(tinyflow);

    }

    /**
     * 指标调用流程
     *
     * @param flowId    流程id
     * @param variables 参数
     * @return map
     */
    public Map<String, Object> invokeWorkflowResult(String flowId, Map<String, Object> variables) {
        OpsAiWorkflow flow = workFlowFeignService.getFlowById(flowId);
        if (flow == null) {
            throw new RuntimeException("工作流不存在");
        }
        Tinyflow tinyflow = new Tinyflow(flow.getContent());
        initProvider(tinyflow);
        Chain chain = tinyflow.toChain();
        return chain.executeForResult(variables);
    }

    /**
     * 执行工作流
     *
     * @param flowId    流程id
     * @param variables 全局参数
     */
    public void invokeWorkflowExecution(String flowId, Map<String, Object> variables, String scheduleId, String execType, String executionId) {

        OpsAiWorkflow flow = workFlowFeignService.getFlowById(flowId);
        if (flow == null) {
            throw new RuntimeException("工作流不存在");
        }
        Tinyflow tinyflow = new Tinyflow(flow.getContent());
        initProvider(tinyflow);
        Chain chain = tinyflow.toChain();
        //添加监听器，获取执行日志
        List<JSONObject> eventList = new ArrayList<>();
        chain.addEventListener((event, chainThis) -> {
            if (event instanceof ChainStatusChangeEvent) {
                ChainStatus status = ((ChainStatusChangeEvent) event).getStatus();
                if (ChainStatus.FINISHED_ABNORMAL.equals(status)) {
                    String message = chainThis.getMessage();
                    JSONObject content = new JSONObject();
                    content.set("status", "error");
                    content.set("errorMsg", message);
                    // content.set("nodeId", node.getId());
                    eventList.add(content);
                }
            }
        });

        chain.addNodeErrorListener((e, node, map, chainThis) -> {
            String message = ExceptionUtil.getRootCauseMessage(e);
            JSONObject content = new JSONObject();
            content.set("nodeId", node.getId());
            content.set("status", "nodeError");
            content.set("errorMsg", message);

            eventList.add(content);
        });

        executeForRecord(flow, variables, chain, execType, scheduleId, executionId, eventList);

    }

    private void executeForRecord(OpsAiWorkflow flow, Map<String, Object> variables, Chain chain, String execType, String scheduleId, String executionId, List<JSONObject> eventList) {
        //日志写入
        JSONObject logJson = new JSONObject();
        logJson.set("flowId", flow.getId());
        logJson.set("flowName", flow.getTitle());
        logJson.set("startTime", DateUtil.date());
        try {
            chain.executeForResult(variables);
            logJson.set("errorMsg", chain.getStatus().equals(ChainStatus.FINISHED_ABNORMAL) ? chain.getMessage() : "");
        } catch (Exception e) {
            logJson.set("errorMsg", ExceptionUtil.getRootCauseMessage(e));
        }
        logJson.set("endTime", DateUtil.date());
        logJson.set("duration", DateUtil.between(logJson.getDate("startTime"), logJson.getDate("endTime"), DateUnit.MS));
        logJson.set("status", chain.getStatus());
        logJson.set("resultSummary", chain.getStatus().name());
        logJson.set("triggerType", execType);
        logJson.set("scheduleId", scheduleId);
        logJson.set("executionId", executionId);
        logSummaryService.saveWorkflowLogSummaryByWorkflowExecute(logJson, eventList);
    }

    /**
     * 执行工作流用于触发器调用
     * 工作流必须在返回的Map中包含"triggerResult"字段（Boolean类型）
     * 可选包含"taskInfo"字段（Map类型），用于传递任务创建信息
     * 如果没有triggerResult字段或值不是Boolean类型，默认返回false
     *
     * @param flowId 工作流ID
     * @param variables 执行参数
     * @return 触发器执行结果
     */
    public WorkflowTriggerResultVO executeWorkflowForTrigger(String flowId, Map<String, Object> variables) {
        try {
            Map<String, Object> result = invokeWorkflowResult(flowId, variables);
            Boolean triggerResult = (Boolean) result.get("triggerResult");

            WorkflowTriggerResultVO resultVO = new WorkflowTriggerResultVO();
            resultVO.setTriggerResult(triggerResult != null ? triggerResult : false);

            // 解析工作流返回的任务信息（可选）
            Object taskInfoObj = result.get("taskInfo");
            if (taskInfoObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> taskInfo = (Map<String, Object>) taskInfoObj;
                resultVO.setTaskInfo(taskInfo);
            }

            return resultVO;
        } catch (Exception e) {
            // 工作流执行失败时，记录日志并返回false
            WorkflowTriggerResultVO resultVO = new WorkflowTriggerResultVO();
            resultVO.setTriggerResult(false);
            resultVO.setMessage("工作流执行失败: " + e.getMessage());
            return resultVO;
        }
    }

}
